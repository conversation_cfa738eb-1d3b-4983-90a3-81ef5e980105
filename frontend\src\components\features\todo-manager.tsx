'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence, Reorder } from 'framer-motion';
import { Plus, Check, X, Edit2, Calendar, Flag, Clock, Trash2, GripVertical } from 'lucide-react';
import { AnimatedButton } from '@/components/ui/animated-button';
import { Animated<PERSON><PERSON>, Animated<PERSON>ardContent, AnimatedCardHeader, AnimatedCardTitle } from '@/components/ui/animated-card';
import { Form, FormInput, FormTextarea, FormSelect } from '@/components/ui/form';
import { useRealtime } from '@/lib/realtime-context';
import { todosApi } from '@/lib/api-client';
import { toast } from 'react-hot-toast';
import { cn } from '@/lib/utils';
import { TodoSource, Priority, TodoStatus } from '@/types';
import * as z from 'zod';

const todoSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  deadline: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']),
});

interface Todo {
  id: string;
  title: string;
  description?: string;
  deadline?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'completed';
  createdAt: string;
  updatedAt: string;
}

export function TodoManager() {
  const { todos: realtimeTodos } = useRealtime();
  const [todos, setTodos] = useState<Todo[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'completed'>('all');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchTodos();
  }, []);

  // Update todos when real-time data changes
  useEffect(() => {
    if (realtimeTodos) {
      setTodos(realtimeTodos);
    }
  }, [realtimeTodos]);

  const fetchTodos = async () => {
    try {
      setIsLoading(true);
      const response = await todosApi.getAll();
      if (response.success && response.data) {
        setTodos(response.data as Todo[]);
      }
    } catch (error) {
      console.error('Error fetching todos:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTodo = async (data: z.infer<typeof todoSchema>) => {
    try {
      const response = await todosApi.create({
        ...data,
        deadline: data.deadline ? new Date(data.deadline) : undefined,
        source: TodoSource.PERSONAL,
        priority: data.priority === 'low' ? Priority.LOW :
                 data.priority === 'medium' ? Priority.MEDIUM : Priority.HIGH,
      });

      if (response.success) {
        setIsCreating(false);
        // Real-time update will handle adding to list
      }
    } catch (error) {
      console.error('Error creating todo:', error);
    }
  };

  const handleUpdateTodo = async (id: string, data: Partial<Todo>) => {
    try {
      // Convert deadline to Date if it's a string and map status
      const updateData = {
        ...data,
        deadline: data.deadline && typeof data.deadline === 'string' ? new Date(data.deadline) : data.deadline as Date | undefined,
        status: data.status === 'pending' ? TodoStatus.PENDING :
                data.status === 'completed' ? TodoStatus.COMPLETED : data.status
      };
      const response = await todosApi.update(id, updateData as any);
      if (response.success) {
        setEditingId(null);
        // Real-time update will handle updating the list
      }
    } catch (error) {
      console.error('Error updating todo:', error);
    }
  };

  const handleToggleComplete = async (todo: Todo) => {
    const newStatus = todo.status === 'completed' ? 'pending' : 'completed';
    await handleUpdateTodo(todo.id, { status: newStatus });
  };

  const handleDeleteTodo = async (id: string) => {
    try {
      const response = await todosApi.delete(id);
      if (response.success) {
        // Real-time update will handle removing from list
      }
    } catch (error) {
      console.error('Error deleting todo:', error);
    }
  };

  const handleReorder = async (newTodos: Todo[]) => {
    setTodos(newTodos);
    try {
      await todosApi.reorder(newTodos.map(todo => todo.id));
    } catch (error) {
      console.error('Error reordering todos:', error);
      // Revert on error
      fetchTodos();
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-100 border-green-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const filteredTodos = todos.filter(todo => {
    if (filter === 'all') return true;
    return todo.status === filter;
  });

  if (isLoading) {
    return (
      <AnimatedCard>
        <AnimatedCardHeader>
          <AnimatedCardTitle>Todo Manager</AnimatedCardTitle>
        </AnimatedCardHeader>
        <AnimatedCardContent>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </AnimatedCardContent>
      </AnimatedCard>
    );
  }

  return (
    <AnimatedCard className="h-full">
      <AnimatedCardHeader>
        <div className="flex items-center justify-between">
          <AnimatedCardTitle className="flex items-center gap-2">
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Check className="h-5 w-5 text-green-600" />
            </motion.div>
            Todo Manager
          </AnimatedCardTitle>
          
          <div className="flex items-center gap-2">
            {/* Filter */}
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="all">All</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
            </select>
            
            <AnimatedButton
              size="sm"
              onClick={() => setIsCreating(true)}
              icon={<Plus className="h-4 w-4" />}
            >
              Add Todo
            </AnimatedButton>
          </div>
        </div>
      </AnimatedCardHeader>

      <AnimatedCardContent className="space-y-4">
        {/* Create Todo Form */}
        <AnimatePresence>
          {isCreating && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border border-gray-200 rounded-lg p-4 bg-gray-50"
            >
              <Form
                schema={todoSchema}
                onSubmit={handleCreateTodo}
                className="space-y-4"
              >
                <FormInput
                  name="title"
                  label="Title"
                  placeholder="Enter todo title..."
                  required
                />
                
                <FormTextarea
                  name="description"
                  label="Description"
                  placeholder="Enter description (optional)..."
                  rows={3}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <FormInput
                    name="deadline"
                    label="Deadline"
                    type="datetime-local"
                  />
                  
                  <FormSelect
                    name="priority"
                    label="Priority"
                    options={[
                      { value: 'low', label: 'Low' },
                      { value: 'medium', label: 'Medium' },
                      { value: 'high', label: 'High' },
                    ]}
                    required
                  />
                </div>
                
                <div className="flex justify-end gap-2">
                  <AnimatedButton
                    type="button"
                    variant="ghost"
                    onClick={() => setIsCreating(false)}
                  >
                    Cancel
                  </AnimatedButton>
                  <AnimatedButton type="submit">
                    Create Todo
                  </AnimatedButton>
                </div>
              </Form>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Todo List */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          <Reorder.Group
            axis="y"
            values={filteredTodos}
            onReorder={handleReorder}
            className="space-y-2"
          >
            <AnimatePresence>
              {filteredTodos.map((todo) => (
                <Reorder.Item
                  key={todo.id}
                  value={todo}
                  className="cursor-grab active:cursor-grabbing"
                >
                  <motion.div
                    layout
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className={cn(
                      "border rounded-lg p-3 bg-white hover:shadow-md transition-shadow",
                      todo.status === 'completed' && "opacity-60"
                    )}
                  >
                    <div className="flex items-start gap-3">
                      <GripVertical className="h-4 w-4 text-gray-400 mt-1 flex-shrink-0" />
                      
                      <motion.button
                        onClick={() => handleToggleComplete(todo)}
                        className={cn(
                          "w-5 h-5 rounded border-2 flex items-center justify-center mt-0.5 flex-shrink-0",
                          todo.status === 'completed'
                            ? "bg-green-500 border-green-500"
                            : "border-gray-300 hover:border-green-500"
                        )}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        {todo.status === 'completed' && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ type: "spring", stiffness: 500 }}
                          >
                            <Check className="h-3 w-3 text-white" />
                          </motion.div>
                        )}
                      </motion.button>
                      
                      <div className="flex-1 min-w-0">
                        <h4 className={cn(
                          "font-medium text-gray-900",
                          todo.status === 'completed' && "line-through"
                        )}>
                          {todo.title}
                        </h4>
                        
                        {todo.description && (
                          <p className="text-sm text-gray-600 mt-1">
                            {todo.description}
                          </p>
                        )}
                        
                        <div className="flex items-center gap-4 mt-2">
                          <span className={cn(
                            "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border",
                            getPriorityColor(todo.priority)
                          )}>
                            <Flag className="h-3 w-3" />
                            {todo.priority}
                          </span>
                          
                          {todo.deadline && (
                            <span className="inline-flex items-center gap-1 text-xs text-gray-500">
                              <Calendar className="h-3 w-3" />
                              {new Date(todo.deadline).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <motion.button
                          onClick={() => setEditingId(todo.id)}
                          className="p-1 text-gray-400 hover:text-blue-600 rounded"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Edit2 className="h-4 w-4" />
                        </motion.button>
                        
                        <motion.button
                          onClick={() => handleDeleteTodo(todo.id)}
                          className="p-1 text-gray-400 hover:text-red-600 rounded"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                </Reorder.Item>
              ))}
            </AnimatePresence>
          </Reorder.Group>
          
          {filteredTodos.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-8 text-gray-500"
            >
              <Check className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No todos found</p>
              <p className="text-sm">Create your first todo to get started!</p>
            </motion.div>
          )}
        </div>
      </AnimatedCardContent>
    </AnimatedCard>
  );
}
