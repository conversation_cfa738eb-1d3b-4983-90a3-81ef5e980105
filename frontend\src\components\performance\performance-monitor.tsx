'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
  loadTime: number;
  domContentLoaded: number;
}

interface PerformanceMonitorProps {
  showMetrics?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

export function PerformanceMonitor({ 
  showMetrics = false, 
  position = 'bottom-right' 
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;

    const measurePerformance = () => {
      // Get navigation timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        setMetrics(prev => ({
          ...prev,
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          ttfb: navigation.responseStart - navigation.requestStart,
        }));
      }

      // Get paint timing
      const paintEntries = performance.getEntriesByType('paint');
      paintEntries.forEach((entry) => {
        if (entry.name === 'first-contentful-paint') {
          setMetrics(prev => ({ ...prev, fcp: entry.startTime }));
        }
      });

      // Get LCP
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
      });
      
      try {
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        // LCP not supported
      }

      // Get FID
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          setMetrics(prev => ({ ...prev, fid: (entry as any).processingStart - entry.startTime }));
        });
      });
      
      try {
        fidObserver.observe({ entryTypes: ['first-input'] });
      } catch (e) {
        // FID not supported
      }

      // Get CLS
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        setMetrics(prev => ({ ...prev, cls: clsValue }));
      });
      
      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        // CLS not supported
      }

      return () => {
        observer.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    };

    const cleanup = measurePerformance();
    
    // Show metrics after a delay
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 2000);

    return () => {
      cleanup?.();
      clearTimeout(timer);
    };
  }, []);

  const getScoreColor = (metric: string, value: number) => {
    switch (metric) {
      case 'fcp':
        return value < 1800 ? 'text-green-500' : value < 3000 ? 'text-yellow-500' : 'text-red-500';
      case 'lcp':
        return value < 2500 ? 'text-green-500' : value < 4000 ? 'text-yellow-500' : 'text-red-500';
      case 'fid':
        return value < 100 ? 'text-green-500' : value < 300 ? 'text-yellow-500' : 'text-red-500';
      case 'cls':
        return value < 0.1 ? 'text-green-500' : value < 0.25 ? 'text-yellow-500' : 'text-red-500';
      case 'ttfb':
        return value < 800 ? 'text-green-500' : value < 1800 ? 'text-yellow-500' : 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const formatMetric = (value: number, unit: string = 'ms') => {
    if (unit === 'ms') {
      return `${Math.round(value)}ms`;
    }
    return value.toFixed(3);
  };

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };

  if (!showMetrics || process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className={`fixed ${positionClasses[position]} z-50 bg-black/80 backdrop-blur-sm text-white p-4 rounded-lg shadow-lg max-w-xs`}
        >
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold">Performance</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              ×
            </button>
          </div>
          
          <div className="space-y-2 text-xs">
            {metrics.fcp && (
              <div className="flex justify-between">
                <span>FCP:</span>
                <span className={getScoreColor('fcp', metrics.fcp)}>
                  {formatMetric(metrics.fcp)}
                </span>
              </div>
            )}
            
            {metrics.lcp && (
              <div className="flex justify-between">
                <span>LCP:</span>
                <span className={getScoreColor('lcp', metrics.lcp)}>
                  {formatMetric(metrics.lcp)}
                </span>
              </div>
            )}
            
            {metrics.fid && (
              <div className="flex justify-between">
                <span>FID:</span>
                <span className={getScoreColor('fid', metrics.fid)}>
                  {formatMetric(metrics.fid)}
                </span>
              </div>
            )}
            
            {metrics.cls !== undefined && (
              <div className="flex justify-between">
                <span>CLS:</span>
                <span className={getScoreColor('cls', metrics.cls)}>
                  {formatMetric(metrics.cls, 'score')}
                </span>
              </div>
            )}
            
            {metrics.ttfb && (
              <div className="flex justify-between">
                <span>TTFB:</span>
                <span className={getScoreColor('ttfb', metrics.ttfb)}>
                  {formatMetric(metrics.ttfb)}
                </span>
              </div>
            )}
            
            {metrics.loadTime && (
              <div className="flex justify-between">
                <span>Load:</span>
                <span className="text-blue-400">
                  {formatMetric(metrics.loadTime)}
                </span>
              </div>
            )}
          </div>
          
          <div className="mt-3 pt-2 border-t border-gray-600">
            <div className="text-xs text-gray-400">
              Dev Mode Only
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Performance optimization tips component
export function PerformanceTips() {
  const [showTips, setShowTips] = useState(false);

  useEffect(() => {
    // Show tips if performance is poor
    const checkPerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation && navigation.loadEventEnd - navigation.loadEventStart > 3000) {
        setShowTips(true);
      }
    };

    setTimeout(checkPerformance, 3000);
  }, []);

  if (!showTips || process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-4 left-4 bg-yellow-50 border border-yellow-200 p-4 rounded-lg shadow-lg max-w-sm"
    >
      <div className="flex items-start">
        <div className="text-yellow-600 mr-2">⚡</div>
        <div>
          <h4 className="font-semibold text-yellow-800 mb-1">Performance Tip</h4>
          <p className="text-sm text-yellow-700">
            Page load time is high. Consider optimizing images and reducing bundle size.
          </p>
          <button
            onClick={() => setShowTips(false)}
            className="text-xs text-yellow-600 hover:text-yellow-800 mt-2"
          >
            Dismiss
          </button>
        </div>
      </div>
    </motion.div>
  );
}

// Resource hints component
export function ResourceHints() {
  useEffect(() => {
    // Add resource hints for better performance
    const addResourceHint = (rel: string, href: string, as?: string) => {
      const link = document.createElement('link');
      link.rel = rel;
      link.href = href;
      if (as) link.as = as;
      document.head.appendChild(link);
    };

    // DNS prefetch
    addResourceHint('dns-prefetch', '//fonts.googleapis.com');
    addResourceHint('dns-prefetch', '//api.openai.com');
    
    // Preconnect to critical origins
    addResourceHint('preconnect', '//fonts.gstatic.com');
    
    // Prefetch likely next pages
    addResourceHint('prefetch', '/dashboard');
    addResourceHint('prefetch', '/forum');
    addResourceHint('prefetch', '/editor');
  }, []);

  return null;
}
