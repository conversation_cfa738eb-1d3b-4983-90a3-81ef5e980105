const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 5000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3001', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Test backend server is running' });
});

// Mock auth endpoints
app.get('/api/auth/session', (req, res) => {
  res.json({ user: null });
});

app.post('/api/auth/signin', (req, res) => {
  res.json({ 
    success: true, 
    user: { 
      id: '1', 
      email: '<EMAIL>', 
      name: 'Test User',
      role: 'STUDENT'
    } 
  });
});

// Mock dashboard API
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalClasses: 5,
      totalAssignments: 12,
      completedAssignments: 8,
      upcomingDeadlines: 3,
      studyStreak: 7,
      totalStudyTime: 45
    }
  });
});

// Mock classes API
app.get('/api/classes', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Advanced React Development',
        description: 'Learn modern React patterns and hooks',
        subject: 'Computer Science',
        studentCount: 24,
        assignmentCount: 8,
        createdAt: '2024-01-15',
        color: 'bg-blue-500'
      },
      {
        id: '2',
        name: 'Database Design',
        description: 'Master SQL and database optimization',
        subject: 'Computer Science',
        studentCount: 18,
        assignmentCount: 6,
        createdAt: '2024-01-20',
        color: 'bg-green-500'
      }
    ]
  });
});

app.get('/api/classes/:id', (req, res) => {
  res.json({
    success: true,
    data: {
      id: req.params.id,
      name: 'Advanced React Development',
      description: 'Learn modern React patterns and hooks',
      subject: 'Computer Science',
      studentCount: 24,
      assignmentCount: 8,
      createdAt: '2024-01-15',
      color: 'bg-blue-500'
    }
  });
});

// Mock assignments API
app.get('/api/assignments', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        title: 'React Hooks Deep Dive',
        description: 'Create a complex application using useState, useEffect, and custom hooks',
        dueDate: '2024-02-15',
        priority: 'high',
        status: 'published',
        completedCount: 18,
        totalStudents: 24,
        createdAt: '2024-01-20',
        classId: '1'
      },
      {
        id: '2',
        title: 'State Management with Redux',
        description: 'Implement Redux in a React application',
        dueDate: '2024-02-20',
        priority: 'medium',
        status: 'published',
        completedCount: 12,
        totalStudents: 24,
        createdAt: '2024-01-25',
        classId: '1'
      }
    ]
  });
});

app.post('/api/assignments', (req, res) => {
  res.json({
    success: true,
    data: {
      id: '3',
      ...req.body,
      createdAt: new Date().toISOString()
    }
  });
});

// Mock todos API
app.get('/api/todos', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        title: 'Complete React assignment',
        description: 'Finish the hooks exercise',
        deadline: '2024-02-15T23:59:59Z',
        priority: 'HIGH',
        status: 'PENDING',
        source: 'MANUAL',
        createdAt: '2024-01-20T10:00:00Z'
      },
      {
        id: '2',
        title: 'Study for database exam',
        description: 'Review SQL queries and normalization',
        deadline: '2024-02-18T09:00:00Z',
        priority: 'MEDIUM',
        status: 'PENDING',
        source: 'MANUAL',
        createdAt: '2024-01-22T14:30:00Z'
      }
    ]
  });
});

app.post('/api/todos', (req, res) => {
  res.json({
    success: true,
    data: {
      id: Date.now().toString(),
      ...req.body,
      createdAt: new Date().toISOString()
    }
  });
});

app.put('/api/todos/:id', (req, res) => {
  res.json({
    success: true,
    data: {
      id: req.params.id,
      ...req.body,
      updatedAt: new Date().toISOString()
    }
  });
});

app.delete('/api/todos/:id', (req, res) => {
  res.json({
    success: true,
    message: 'Todo deleted successfully'
  });
});

// Mock chatbot API
app.post('/api/chatbot/chat', (req, res) => {
  const { message } = req.body;
  res.json({
    success: true,
    data: {
      response: `I understand you said: "${message}". This is a mock response from the chatbot API. The real AI chatbot would provide more intelligent responses.`,
      timestamp: new Date().toISOString()
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Test backend server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});
