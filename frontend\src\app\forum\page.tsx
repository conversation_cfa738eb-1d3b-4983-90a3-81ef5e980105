'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Header } from '@/components/layout/header';
import { AnimatedButton } from '@/components/ui/animated-button';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, Animated<PERSON><PERSON><PERSON>eader, AnimatedCardTitle } from '@/components/ui/animated-card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AvatarPlaceholder } from '@/components/ui/avatar-placeholder';
import { NeuralBackground, GridBackground } from '@/components/effects/background-effects';
import { FloatingParticles } from '@/components/effects/particle-system';
import {
  MessageSquare,
  Heart,
  Share,
  Bookmark,
  Search,
  Filter,
  Plus,
  TrendingUp,
  Clock,
  Users,
  Star,
  Eye,
  ThumbsUp,
  MessageCircle,
  Flame,
  Zap,
  Award
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { forumApi } from '@/lib/api-client';
import { fadeInUp, staggerContainer, staggerItem, magneticHover } from '@/lib/animations';

interface ForumPost {
  id: string;
  title: string;
  content: string;
  author: {
    name: string;
    avatar: string;
    role: string;
  };
  category: string;
  likes: number;
  comments: number;
  views: number;
  createdAt: Date;
  isLiked: boolean;
  isBookmarked: boolean;
  tags: string[];
}

export default function ForumPage() {
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('latest');
  const [isLoading, setIsLoading] = useState(true);

  const categories = [
    { id: 'all', name: 'All Posts', icon: <MessageSquare className="h-4 w-4" />, count: 1234 },
    { id: 'programming', name: 'Programming', icon: <Zap className="h-4 w-4" />, count: 456 },
    { id: 'ai-ml', name: 'AI & ML', icon: <Star className="h-4 w-4" />, count: 234 },
    { id: 'career', name: 'Career', icon: <Award className="h-4 w-4" />, count: 123 },
    { id: 'projects', name: 'Projects', icon: <Flame className="h-4 w-4" />, count: 89 },
    { id: 'community', name: 'Community', icon: <Users className="h-4 w-4" />, count: 67 },
  ];

  const mockPosts: ForumPost[] = [
    {
      id: '1',
      title: 'How to master React Hooks in 2024?',
      content: 'I\'ve been learning React for a few months now and I\'m struggling with hooks. Any tips or resources you\'d recommend?',
      author: {
        name: 'Nguyen Van A',
        avatar: '/avatars/student1.jpg',
        role: 'Student'
      },
      category: 'programming',
      likes: 24,
      comments: 12,
      views: 156,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isLiked: false,
      isBookmarked: true,
      tags: ['React', 'JavaScript', 'Frontend']
    },
    {
      id: '2',
      title: 'AI Project Ideas for Final Year Students',
      content: 'Looking for innovative AI project ideas that would be suitable for a final year computer science project. Any suggestions?',
      author: {
        name: 'Tran Thi B',
        avatar: '/avatars/student2.jpg',
        role: 'Student'
      },
      category: 'ai-ml',
      likes: 45,
      comments: 23,
      views: 289,
      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
      isLiked: true,
      isBookmarked: false,
      tags: ['AI', 'Machine Learning', 'Projects']
    },
    {
      id: '3',
      title: 'Internship Experience at FPT Software',
      content: 'Just completed my internship at FPT Software. Happy to share my experience and answer any questions!',
      author: {
        name: 'Le Van C',
        avatar: '/avatars/student3.jpg',
        role: 'Alumni'
      },
      category: 'career',
      likes: 67,
      comments: 34,
      views: 445,
      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
      isLiked: false,
      isBookmarked: true,
      tags: ['Internship', 'FPT', 'Career']
    }
  ];

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setPosts(mockPosts);
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleLike = async (postId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      // Simulate API call for demo - remove when backend is ready
      if (post.isLiked) {
        // await forumApi.unlikePost(postId);
        setPosts(posts.map(p =>
          p.id === postId
            ? { ...p, isLiked: false, likes: p.likes - 1 }
            : p
        ));
        toast.success('Unliked post!');
      } else {
        // await forumApi.likePost(postId);
        setPosts(posts.map(p =>
          p.id === postId
            ? { ...p, isLiked: true, likes: p.likes + 1 }
            : p
        ));
        toast.success('Liked post!');
      }
    } catch (error) {
      toast.error('Failed to update like');
    }
  };

  const handleBookmark = (postId: string) => {
    setPosts(posts.map(p => 
      p.id === postId 
        ? { ...p, isBookmarked: !p.isBookmarked }
        : p
    ));
    toast.success('Bookmark updated!');
  };

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const sortedPosts = [...filteredPosts].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.likes - a.likes;
      case 'comments':
        return b.comments - a.comments;
      case 'views':
        return b.views - a.views;
      default:
        return b.createdAt.getTime() - a.createdAt.getTime();
    }
  });

  return (
    <div className="min-h-screen relative">
      {/* Lightweight Background Effects */}
      <NeuralBackground intensity="low" />
      <GridBackground intensity="low" />
      <FloatingParticles particleCount={8} colors={['#3b82f6', '#8b5cf6', '#06b6d4']} />

      <Header />

      <div className="container mx-auto px-4 py-8">
        {/* Forum Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-4"
            animate={{
              backgroundImage: [
                "linear-gradient(45deg, #3b82f6, #8b5cf6)",
                "linear-gradient(45deg, #8b5cf6, #06b6d4)",
                "linear-gradient(45deg, #06b6d4, #3b82f6)"
              ]
            }}
            transition={{ duration: 5, repeat: Infinity }}
            style={{
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              color: 'transparent'
            }}
          >
            FPT UniHub Forum
          </motion.h1>
          <motion.p
            className="text-xl text-muted-foreground max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            Connect, share knowledge, and learn together with the FPT University community
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            {/* Create Post Button */}
            <motion.div className="mb-6" whileHover={{ scale: 1.02 }}>
              <AnimatedButton
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                animation="pulse"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Post
              </AnimatedButton>
            </motion.div>

            {/* Categories */}
            <AnimatedCard className="mb-6">
              <AnimatedCardHeader>
                <AnimatedCardTitle className="flex items-center">
                  <Filter className="h-4 w-4 mr-2" />
                  Categories
                </AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent className="space-y-2">
                {categories.map((category, index) => (
                  <motion.button
                    key={category.id}
                    className={`w-full flex items-center justify-between p-3 rounded-lg transition-all ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'hover:bg-gray-100'
                    }`}
                    onClick={() => setSelectedCategory(category.id)}
                    whileHover={{ scale: 1.05, rotate: 1 }}
                    whileTap={{ scale: 0.95, rotate: -2 }}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="flex items-center">
                      {category.icon}
                      <span className="ml-2 font-medium">{category.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {category.count}
                    </Badge>
                  </motion.button>
                ))}
              </AnimatedCardContent>
            </AnimatedCard>

            {/* Trending Topics */}
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle className="flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Trending Topics
                </AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-2">
                  {['React Hooks', 'AI Projects', 'Internships', 'Web3', 'Mobile Dev'].map((topic, index) => (
                    <motion.div
                      key={topic}
                      className="flex items-center justify-between p-2 rounded hover:bg-gray-50 cursor-pointer"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      whileHover={{ x: 5 }}
                    >
                      <span className="text-sm">{topic}</span>
                      <Flame className="h-3 w-3 text-orange-500" />
                    </motion.div>
                  ))}
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </motion.div>

          {/* Main Content */}
          <motion.div
            className="lg:col-span-3"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            {/* Search and Sort */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search posts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border rounded-lg bg-background"
              >
                <option value="latest">Latest</option>
                <option value="popular">Most Popular</option>
                <option value="comments">Most Comments</option>
                <option value="views">Most Views</option>
              </select>
            </div>

            {/* Posts */}
            <motion.div
              className="space-y-6"
              variants={staggerContainer}
              initial="initial"
              animate="animate"
            >
              <AnimatePresence>
                {isLoading ? (
                  // Loading skeletons
                  Array.from({ length: 3 }).map((_, index) => (
                    <motion.div
                      key={index}
                      className="bg-white rounded-xl p-6 border animate-pulse"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-24"></div>
                          <div className="h-3 bg-gray-200 rounded w-16"></div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </motion.div>
                  ))
                ) : (
                  sortedPosts.map((post, index) => (
                    <motion.div
                      key={post.id}
                      variants={staggerItem}
                      layout
                      whileHover={{ y: -5 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <AnimatedCard className="overflow-hidden hover:shadow-lg transition-shadow">
                        <AnimatedCardContent className="p-6">
                          {/* Post Header */}
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center space-x-3">
                              <AvatarPlaceholder name={post.author.name} size="md" />
                              <div>
                                <p className="font-medium">{post.author.name}</p>
                                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                  <Badge variant="outline" className="text-xs">
                                    {post.author.role}
                                  </Badge>
                                  <span>•</span>
                                  <Clock className="h-3 w-3" />
                                  <span>{Math.floor((Date.now() - post.createdAt.getTime()) / (1000 * 60 * 60))}h ago</span>
                                </div>
                              </div>
                            </div>
                            <Badge>{categories.find(c => c.id === post.category)?.name}</Badge>
                          </div>

                          {/* Post Content */}
                          <div className="mb-4">
                            <h3 className="text-xl font-semibold mb-2 hover:text-blue-600 cursor-pointer transition-colors">
                              {post.title}
                            </h3>
                            <p className="text-muted-foreground line-clamp-2">{post.content}</p>
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-2 mb-4">
                            {post.tags.map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                #{tag}
                              </Badge>
                            ))}
                          </div>

                          {/* Post Actions */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <motion.button
                                className={`flex items-center space-x-1 text-sm transition-colors ${
                                  post.isLiked ? 'text-red-500' : 'text-muted-foreground hover:text-red-500'
                                }`}
                                onClick={() => handleLike(post.id)}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Heart className={`h-4 w-4 ${post.isLiked ? 'fill-current' : ''}`} />
                                <span>{post.likes}</span>
                              </motion.button>

                              <motion.button
                                className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-green-500 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <ThumbsUp className="h-4 w-4" />
                                <span>Helpful</span>
                              </motion.button>

                              <motion.button
                                className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-blue-500 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <MessageCircle className="h-4 w-4" />
                                <span>{post.comments}</span>
                              </motion.button>

                              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                                <Eye className="h-4 w-4" />
                                <span>{post.views}</span>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <motion.button
                                className={`p-2 rounded-lg transition-colors ${
                                  post.isBookmarked ? 'text-yellow-500 bg-yellow-50' : 'text-muted-foreground hover:text-yellow-500'
                                }`}
                                onClick={() => handleBookmark(post.id)}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Bookmark className={`h-4 w-4 ${post.isBookmarked ? 'fill-current' : ''}`} />
                              </motion.button>

                              <motion.button
                                className="p-2 rounded-lg text-muted-foreground hover:text-blue-500 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Share className="h-4 w-4" />
                              </motion.button>
                            </div>
                          </div>
                        </AnimatedCardContent>
                      </AnimatedCard>
                    </motion.div>
                  ))
                )}
              </AnimatePresence>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
