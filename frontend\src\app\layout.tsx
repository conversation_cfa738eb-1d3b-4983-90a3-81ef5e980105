import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from 'next/font/google';
import { SessionProvider } from 'next-auth/react';
import { AuthProvider } from '@/lib/auth-context';
import { RealtimeProvider } from '@/lib/realtime-context';
import { ThemeProvider } from '@/lib/theme-provider';
import { Toaster } from 'react-hot-toast';
import { ChatbotFloatingButton } from '@/components/ui/chatbot-floating-button';

import "./globals.css";

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: "FPT UniHub - AI-Powered Learning Platform",
  description: "Complete learning management system with AI-powered features for personalized learning and automatic todo generation.",
  keywords: ["education", "learning", "AI", "todo", "assignments", "FPT University"],
  authors: [{ name: "FPT UniHub Team" }],
  creator: "FPT UniHub",
  publisher: "FPT UniHub",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <SessionProvider>
            <AuthProvider>
              <RealtimeProvider>
                {children}

                {/* Global Chatbot Floating Button */}
                <ChatbotFloatingButton />

                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: 'hsl(var(--card))',
                      color: 'hsl(var(--card-foreground))',
                      border: '1px solid hsl(var(--border))',
                    },
                  }}
                />
              </RealtimeProvider>
            </AuthProvider>
          </SessionProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
