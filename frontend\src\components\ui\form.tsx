'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { useForm, FormProvider, useFormContext, FieldPath, FieldValues } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { cn } from '@/lib/utils';
import { Label } from './label';
import { Input } from './input';
import { fadeInUp } from '@/lib/animations';

interface FormProps<T extends FieldValues> extends Omit<React.FormHTMLAttributes<HTMLFormElement>, 'onSubmit'> {
  schema?: any;
  defaultValues?: Partial<T>;
  onSubmit: (data: T) => void | Promise<void>;
  children: React.ReactNode;
}

export function Form<T extends FieldValues>({
  schema,
  defaultValues,
  onSubmit,
  children,
  className,
  ...props
}: FormProps<T>) {
  const methods = useForm<T>({
    resolver: schema ? zodResolver(schema) : undefined,
    defaultValues,
  });

  // Filter out conflicting props
  const { onDrag, onDragEnd, onDragStart, onAnimationStart, onAnimationEnd, ...motionProps } = props as any;

  return (
    <FormProvider {...methods}>
      <motion.form
        onSubmit={methods.handleSubmit(onSubmit)}
        className={cn('space-y-6', className)}
        {...fadeInUp}
        {...motionProps}
      >
        {children}
      </motion.form>
    </FormProvider>
  );
}

interface FormFieldProps {
  name: string;
  label?: string;
  description?: string;
  required?: boolean;
  children: React.ReactNode;
}

export function FormField({ name, label, description, required, children }: FormFieldProps) {
  const { formState: { errors } } = useFormContext();
  const error = errors[name];

  return (
    <motion.div
      className="space-y-2"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {label && (
        <Label htmlFor={name} className="flex items-center gap-1">
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <div className="relative">
        {children}
        
        {/* Error animation */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -10 }}
            className="absolute -bottom-6 left-0"
          >
            <p className="text-sm text-red-600 flex items-center gap-1">
              <motion.span
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 0.5 }}
              >
                ⚠️
              </motion.span>
              {error.message as string}
            </p>
          </motion.div>
        )}
      </div>
      
      {description && !error && (
        <p className="text-sm text-gray-500">{description}</p>
      )}
    </motion.div>
  );
}

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string;
  label?: string;
  description?: string;
  required?: boolean;
}

export function FormInput({ name, label, description, required, ...props }: FormInputProps) {
  const { register, formState: { errors } } = useFormContext();
  const error = errors[name];

  return (
    <FormField name={name} label={label} description={description} required={required}>
      <motion.div
        whileFocus={{ scale: 1.02 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <Input
          {...register(name)}
          {...props}
          className={cn(
            'transition-all duration-200',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            'focus:shadow-lg focus:shadow-blue-500/25'
          )}
        />
      </motion.div>
    </FormField>
  );
}

interface FormTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  name: string;
  label?: string;
  description?: string;
  required?: boolean;
}

export function FormTextarea({ name, label, description, required, className, ...props }: FormTextareaProps) {
  const { register, formState: { errors } } = useFormContext();
  const error = errors[name];

  return (
    <FormField name={name} label={label} description={description} required={required}>
      <motion.div
        whileFocus={{ scale: 1.02 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <textarea
          {...register(name)}
          {...props}
          className={cn(
            'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 resize-none',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            'focus:shadow-lg focus:shadow-blue-500/25',
            className
          )}
        />
      </motion.div>
    </FormField>
  );
}

interface FormSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  name: string;
  label?: string;
  description?: string;
  required?: boolean;
  options: { value: string; label: string }[];
}

export function FormSelect({ name, label, description, required, options, className, ...props }: FormSelectProps) {
  const { register, formState: { errors } } = useFormContext();
  const error = errors[name];

  return (
    <FormField name={name} label={label} description={description} required={required}>
      <motion.div
        whileFocus={{ scale: 1.02 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <select
          {...register(name)}
          {...props}
          className={cn(
            'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            'focus:shadow-lg focus:shadow-blue-500/25',
            className
          )}
        >
          <option value="">Select an option</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </motion.div>
    </FormField>
  );
}

interface FormCheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string;
  label?: string;
  description?: string;
}

export function FormCheckbox({ name, label, description, className, ...props }: FormCheckboxProps) {
  const { register, formState: { errors } } = useFormContext();
  const error = errors[name];

  return (
    <motion.div
      className="flex items-start space-x-3"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        whileTap={{ scale: 0.9 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <input
          type="checkbox"
          {...register(name)}
          {...props}
          className={cn(
            'h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 transition-all duration-200',
            error && 'border-red-500',
            className
          )}
        />
      </motion.div>
      
      <div className="flex-1">
        {label && (
          <Label htmlFor={name} className="text-sm font-medium text-gray-700">
            {label}
          </Label>
        )}
        {description && (
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        )}
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-600 mt-1"
          >
            {error.message as string}
          </motion.p>
        )}
      </div>
    </motion.div>
  );
}
