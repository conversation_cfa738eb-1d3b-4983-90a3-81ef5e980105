import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { 
  ClassWithRelations, 
  AssignmentWithRelations, 
  TodoWithRelations,
  AiLearningPathWithRelations,
  NotificationWithData,
  DashboardStats
} from '@/types';

// Classes Store
interface ClassesState {
  classes: ClassWithRelations[];
  currentClass: ClassWithRelations | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setClasses: (classes: ClassWithRelations[]) => void;
  setCurrentClass: (classData: ClassWithRelations | null) => void;
  addClass: (classData: ClassWithRelations) => void;
  updateClass: (id: string, updates: Partial<ClassWithRelations>) => void;
  removeClass: (id: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useClassesStore = create<ClassesState>()(
  immer((set) => ({
    classes: [],
    currentClass: null,
    isLoading: false,
    error: null,

    setClasses: (classes) => set((state) => {
      state.classes = classes;
    }),

    setCurrentClass: (classData) => set((state) => {
      state.currentClass = classData;
    }),

    addClass: (classData) => set((state) => {
      state.classes.unshift(classData);
    }),

    updateClass: (id, updates) => set((state) => {
      const index = state.classes.findIndex(c => c.id === id);
      if (index !== -1) {
        state.classes[index] = { ...state.classes[index], ...updates };
      }
      if (state.currentClass?.id === id) {
        state.currentClass = { ...state.currentClass, ...updates };
      }
    }),

    removeClass: (id) => set((state) => {
      state.classes = state.classes.filter(c => c.id !== id);
      if (state.currentClass?.id === id) {
        state.currentClass = null;
      }
    }),

    setLoading: (loading) => set((state) => {
      state.isLoading = loading;
    }),

    setError: (error) => set((state) => {
      state.error = error;
    }),

    clearError: () => set((state) => {
      state.error = null;
    }),
  }))
);

// Assignments Store
interface AssignmentsState {
  assignments: AssignmentWithRelations[];
  currentAssignment: AssignmentWithRelations | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setAssignments: (assignments: AssignmentWithRelations[]) => void;
  setCurrentAssignment: (assignment: AssignmentWithRelations | null) => void;
  addAssignment: (assignment: AssignmentWithRelations) => void;
  updateAssignment: (id: string, updates: Partial<AssignmentWithRelations>) => void;
  removeAssignment: (id: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useAssignmentsStore = create<AssignmentsState>()(
  immer((set) => ({
    assignments: [],
    currentAssignment: null,
    isLoading: false,
    error: null,

    setAssignments: (assignments) => set((state) => {
      state.assignments = assignments;
    }),

    setCurrentAssignment: (assignment) => set((state) => {
      state.currentAssignment = assignment;
    }),

    addAssignment: (assignment) => set((state) => {
      state.assignments.unshift(assignment);
    }),

    updateAssignment: (id, updates) => set((state) => {
      const index = state.assignments.findIndex(a => a.id === id);
      if (index !== -1) {
        state.assignments[index] = { ...state.assignments[index], ...updates };
      }
      if (state.currentAssignment?.id === id) {
        state.currentAssignment = { ...state.currentAssignment, ...updates };
      }
    }),

    removeAssignment: (id) => set((state) => {
      state.assignments = state.assignments.filter(a => a.id !== id);
      if (state.currentAssignment?.id === id) {
        state.currentAssignment = null;
      }
    }),

    setLoading: (loading) => set((state) => {
      state.isLoading = loading;
    }),

    setError: (error) => set((state) => {
      state.error = error;
    }),
  }))
);

// Todos Store
interface TodosState {
  todos: TodoWithRelations[];
  isLoading: boolean;
  error: string | null;
  filter: {
    status?: string;
    priority?: string;
    source?: string;
    search?: string;
  };
  
  // Actions
  setTodos: (todos: TodoWithRelations[]) => void;
  addTodo: (todo: TodoWithRelations) => void;
  updateTodo: (id: string, updates: Partial<TodoWithRelations>) => void;
  removeTodo: (id: string) => void;
  setFilter: (filter: Partial<TodosState['filter']>) => void;
  clearFilter: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Computed
  filteredTodos: () => TodoWithRelations[];
}

export const useTodosStore = create<TodosState>()(
  immer((set, get) => ({
    todos: [],
    isLoading: false,
    error: null,
    filter: {},

    setTodos: (todos) => set((state) => {
      state.todos = todos;
    }),

    addTodo: (todo) => set((state) => {
      state.todos.unshift(todo);
    }),

    updateTodo: (id, updates) => set((state) => {
      const index = state.todos.findIndex(t => t.id === id);
      if (index !== -1) {
        state.todos[index] = { ...state.todos[index], ...updates };
      }
    }),

    removeTodo: (id) => set((state) => {
      state.todos = state.todos.filter(t => t.id !== id);
    }),

    setFilter: (filter) => set((state) => {
      state.filter = { ...state.filter, ...filter };
    }),

    clearFilter: () => set((state) => {
      state.filter = {};
    }),

    setLoading: (loading) => set((state) => {
      state.isLoading = loading;
    }),

    setError: (error) => set((state) => {
      state.error = error;
    }),

    filteredTodos: () => {
      const { todos, filter } = get();
      return todos.filter(todo => {
        if (filter.status && (todo as any).status !== filter.status) return false;
        if (filter.priority && todo.priority !== filter.priority) return false;
        if (filter.source && todo.source !== filter.source) return false;
        if (filter.search) {
          const searchLower = filter.search.toLowerCase();
          return (
            todo.title.toLowerCase().includes(searchLower) ||
            todo.description?.toLowerCase().includes(searchLower)
          );
        }
        return true;
      });
    },
  }))
);

// Notifications Store
interface NotificationsState {
  notifications: NotificationWithData[];
  unreadCount: number;
  isLoading: boolean;
  
  // Actions
  setNotifications: (notifications: NotificationWithData[]) => void;
  addNotification: (notification: NotificationWithData) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  setLoading: (loading: boolean) => void;
}

export const useNotificationsStore = create<NotificationsState>()(
  immer((set) => ({
    notifications: [],
    unreadCount: 0,
    isLoading: false,

    setNotifications: (notifications) => set((state) => {
      state.notifications = notifications;
      state.unreadCount = notifications.filter(n => !n.isRead).length;
    }),

    addNotification: (notification) => set((state) => {
      state.notifications.unshift(notification);
      if (!notification.isRead) {
        state.unreadCount += 1;
      }
    }),

    markAsRead: (id) => set((state) => {
      const notification = state.notifications.find(n => n.id === id);
      if (notification && !notification.isRead) {
        notification.isRead = true;
        state.unreadCount -= 1;
      }
    }),

    markAllAsRead: () => set((state) => {
      state.notifications.forEach(n => n.isRead = true);
      state.unreadCount = 0;
    }),

    removeNotification: (id) => set((state) => {
      const notification = state.notifications.find(n => n.id === id);
      if (notification && !notification.isRead) {
        state.unreadCount -= 1;
      }
      state.notifications = state.notifications.filter(n => n.id !== id);
    }),

    setLoading: (loading) => set((state) => {
      state.isLoading = loading;
    }),
  }))
);

// Dashboard Store
interface DashboardState {
  stats: DashboardStats | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setStats: (stats: DashboardStats) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useDashboardStore = create<DashboardState>()(
  immer((set) => ({
    stats: null,
    isLoading: false,
    error: null,

    setStats: (stats) => set((state) => {
      state.stats = stats;
    }),

    setLoading: (loading) => set((state) => {
      state.isLoading = loading;
    }),

    setError: (error) => set((state) => {
      state.error = error;
    }),
  }))
);
