from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from agents.graph import multi_agent_graph
from langchain_core.messages import HumanMessage
import uvicorn

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/chat")
async def chat(request: Request):
    data = await request.json()
    message = data.get("message")
    # Tạo state đầu vào cho multi_agent_graph
    state = {"messages": [HumanMessage(content=message)]}
    # Chạy graph để lấy kết quả
    result = multi_agent_graph.invoke(state)
    response = result.get("response", "Xin lỗi, tôi chưa có câu trả lời phù hợp.")
    return {"response": response}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "Chatbot service is running"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)