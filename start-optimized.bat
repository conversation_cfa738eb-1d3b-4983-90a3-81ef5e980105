@echo off
echo Starting FPT UniHub - Optimized Version
echo =====================================

REM Kill any existing processes on ports 3000, 3001, 5000, 5001
echo Cleaning up existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do taskkill /f /pid %%a 2>nul
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3001" ^| find "LISTENING"') do taskkill /f /pid %%a 2>nul
for /f "tokens=5" %%a in ('netstat -aon ^| find ":5000" ^| find "LISTENING"') do taskkill /f /pid %%a 2>nul
for /f "tokens=5" %%a in ('netstat -aon ^| find ":5001" ^| find "LISTENING"') do taskkill /f /pid %%a 2>nul

REM Clear Next.js cache
echo Clearing Next.js cache...
cd frontend
if exist .next rmdir /s /q .next
if exist node_modules\.cache rmdir /s /q node_modules\.cache
cd ..

REM Start backend server
echo Starting backend server...
start "Backend Server" cmd /k "cd backend && node optimized-server.js"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend server with turbo
echo Starting frontend server with Turbo...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

REM Wait for servers to start
timeout /t 5 /nobreak >nul

REM Open browser
echo Opening browser...
start http://localhost:3001

echo =====================================
echo Servers started successfully!
echo Frontend: http://localhost:3001
echo Backend:  http://localhost:5000
echo =====================================
pause
