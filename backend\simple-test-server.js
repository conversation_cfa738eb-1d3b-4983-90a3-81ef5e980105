const http = require('http');
const url = require('url');

const PORT = 5001;

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  
  res.setHeader('Content-Type', 'application/json');

  // Health check
  if (path === '/health') {
    res.writeHead(200);
    res.end(JSON.stringify({ status: 'OK', message: 'Simple backend server is running' }));
    return;
  }

  // Mock auth endpoints
  if (path === '/api/auth/session') {
    res.writeHead(200);
    res.end(JSON.stringify({ user: null }));
    return;
  }

  if (path === '/api/auth/signin' && req.method === 'POST') {
    res.writeHead(200);
    res.end(JSON.stringify({ 
      success: true, 
      user: { 
        id: '1', 
        email: '<EMAIL>', 
        name: 'Test User',
        role: 'STUDENT'
      } 
    }));
    return;
  }

  // Mock dashboard API
  if (path === '/api/dashboard/stats') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        totalClasses: 5,
        totalAssignments: 12,
        completedAssignments: 8,
        upcomingDeadlines: 3,
        studyStreak: 7,
        totalStudyTime: 45
      }
    }));
    return;
  }

  // Mock todos API
  if (path === '/api/todos') {
    if (req.method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: [
          {
            id: '1',
            title: 'Complete React assignment',
            description: 'Finish the hooks exercise',
            deadline: '2024-02-15T23:59:59Z',
            priority: 'HIGH',
            status: 'PENDING',
            source: 'MANUAL',
            createdAt: '2024-01-20T10:00:00Z'
          },
          {
            id: '2',
            title: 'Study for database exam',
            description: 'Review SQL queries and normalization',
            deadline: '2024-02-18T09:00:00Z',
            priority: 'MEDIUM',
            status: 'PENDING',
            source: 'MANUAL',
            createdAt: '2024-01-22T14:30:00Z'
          }
        ]
      }));
      return;
    }
  }

  // Mock chatbot API
  if (path === '/api/chatbot/chat' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { message } = JSON.parse(body);
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            response: `I understand you said: "${message}". This is a mock response from the chatbot API.`,
            timestamp: new Date().toISOString()
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Default 404
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'Not found' }));
});

server.listen(PORT, () => {
  console.log(`🚀 Simple backend server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
});
