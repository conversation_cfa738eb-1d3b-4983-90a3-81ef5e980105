# Database Configuration
# For development: SQLite (simple setup)
DATABASE_URL="file:./dev.db"
# For production: PostgreSQL (uncomment and configure)
# DATABASE_URL="postgresql://postgres:admin@localhost:5432/fpt_unihub"

# NextAuth.js Configuration (REQUIRED)
NEXTAUTH_URL="http://localhost:3001"
NEXTAUTH_SECRET="fpt-unihub-nextauth-secret-2024-change-in-production"

# Google OAuth Configuration (REQUIRED for Google login)
GOOGLE_CLIENT_ID="your-google-client-id-from-console"
GOOGLE_CLIENT_SECRET="your-google-client-secret-from-console"

# OpenAI API Configuration (REQUIRED for AI Chatbot)
OPENAI_API_KEY="your-openai-api-key-here"
OPENAI_MODEL="gpt-3.5-turbo"

# Backend API Configuration (REQUIRED for real-time features)
NEXT_PUBLIC_API_URL="http://localhost:5000/api"
NEXT_PUBLIC_SOCKET_URL="http://localhost:5001"

# File Upload Configuration (OPTIONAL)
NEXT_PUBLIC_MAX_FILE_SIZE="10485760"
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# Feature Flags (OPTIONAL)
NEXT_PUBLIC_ENABLE_AI_FEATURES="true"
NEXT_PUBLIC_ENABLE_REALTIME="true"
NEXT_PUBLIC_ENABLE_NOTIFICATIONS="true"
NEXT_PUBLIC_ENABLE_FILE_UPLOAD="true"

# Development Configuration (OPTIONAL)
NEXT_PUBLIC_DEBUG_MODE="true"
NEXT_PUBLIC_SHOW_PERFORMANCE_METRICS="false"
