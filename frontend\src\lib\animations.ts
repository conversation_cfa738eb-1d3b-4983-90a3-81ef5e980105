// Animation variants for Framer Motion
export const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -60 },
  transition: { duration: 0.6 }
};

export const fadeInDown = {
  initial: { opacity: 0, y: -60 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 60 },
  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const fadeInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 60 },
  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const fadeInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -60 },
  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const scaleIn = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.8 },
  transition: { duration: 0.5, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const slideInFromBottom = {
  initial: { opacity: 0, y: 100 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 100 },
  transition: { duration: 0.7, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const staggerContainer = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

export const staggerItem = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.5, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const hoverScale = {
  whileHover: { scale: 1.05 },
  whileTap: { scale: 0.95 },
  transition: { type: "spring", stiffness: 400, damping: 17 }
};

export const hoverGlow = {
  whileHover: { 
    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    y: -2
  },
  transition: { duration: 0.3 }
};

export const floatingAnimation = {
  animate: {
    y: [-10, 10, -10],
  },
  transition: {
    duration: 3,
    repeat: Infinity,
  }
};

export const pulseAnimation = {
  animate: {
    scale: [1, 1.05],
  },
  transition: {
    duration: 1,
    repeat: Infinity,
    repeatType: "reverse" as const,
    type: "tween",
  }
};

export const rotateAnimation = {
  animate: {
    rotate: 360,
    transition: {
      duration: 20,
      repeat: Infinity,
      ease: "linear"
    }
  }
};

export const bounceIn = {
  initial: { opacity: 0, scale: 0.3 },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 260,
      damping: 20
    }
  }
};

export const slideInFromTop = {
  initial: { opacity: 0, y: -100 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -100 },
  transition: { duration: 0.6, ease: "easeOut" }
};

export const cardHover = {
  whileHover: { 
    y: -8,
    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
    transition: { duration: 0.3 }
  },
  whileTap: { scale: 0.98 }
};

export const buttonHover = {
  whileHover: { 
    scale: 1.02,
    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)",
    transition: { duration: 0.2 }
  },
  whileTap: { scale: 0.98 }
};

export const iconSpin = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: "linear"
    }
  }
};

export const typewriterAnimation = {
  initial: { width: 0 },
  animate: { width: "100%" },
  transition: { duration: 2, ease: "easeInOut" }
};

export const gradientShift = {
  animate: {
    backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
  },
  transition: {
    duration: 5,
    repeat: Infinity,
  }
};

// Page transition variants
export const pageTransition = {
  initial: { opacity: 0, x: -200 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 200 },
  transition: { duration: 0.5, ease: "easeOut" }
};

export const modalTransition = {
  initial: { opacity: 0, scale: 0.8, y: 50 },
  animate: { opacity: 1, scale: 1, y: 0 },
  exit: { opacity: 0, scale: 0.8, y: 50 },
  transition: { duration: 0.3, ease: "easeOut" }
};

export const drawerTransition = {
  initial: { x: "100%" },
  animate: { x: 0 },
  exit: { x: "100%" },
  transition: { duration: 0.3, ease: "easeOut" }
};

// Loading animations
export const spinnerAnimation = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: "linear"
    }
  }
};

export const dotsLoading = {
  animate: {
    scale: [1, 1.2, 1],
    opacity: [1, 0.5, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// Notification animations
export const notificationSlideIn = {
  initial: { opacity: 0, x: 300, scale: 0.3 },
  animate: { opacity: 1, x: 0, scale: 1 },
  exit: { opacity: 0, x: 300, scale: 0.5 },
  transition: { duration: 0.4, ease: "easeOut" }
};

// Progress bar animation
export const progressBarAnimation = {
  initial: { width: 0 },
  animate: { width: "100%" },
  transition: { duration: 1.5, ease: "easeOut" }
};

// Skeleton loading animation
export const skeletonPulse = {
  animate: {
    opacity: [0.5, 1, 0.5],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// ===== SPECTACULAR ADVANCED ANIMATIONS =====

// Magnetic hover effect
export const magneticHover = {
  whileHover: {
    scale: 1.05,
    rotate: 1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  },
  whileTap: {
    scale: 0.95,
    rotate: -2
  }
};

// Glitch effect animation
export const glitchEffect = {
  animate: {
    x: [0, -2, 2, 0],
    y: [0, 1, -1, 0],
    skewX: [0, 2, -2, 0],
    transition: {
      duration: 0.2,
      repeat: Infinity,
      repeatType: "reverse" as const,
      ease: "easeInOut"
    }
  }
};

// Morphing blob animation
export const morphingBlob = {
  animate: {
    borderRadius: [
      "60% 40% 30% 70%/60% 30% 70% 40%",
      "30% 60% 70% 40%/50% 60% 30% 60%",
      "40% 60% 60% 40%/60% 40% 60% 40%",
      "60% 40% 30% 70%/60% 30% 70% 40%"
    ],
    scale: [1, 1.1, 0.9, 1],
    rotate: [0, 90, 180, 360],
    transition: {
      duration: 8,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// Particle explosion effect
export const particleExplosion = {
  initial: { scale: 0, opacity: 0 },
  animate: {
    scale: [0, 1.2, 0],
    opacity: [0, 1, 0],
    rotate: [0, 180, 360],
    transition: {
      duration: 1.5,
      ease: "easeOut"
    }
  }
};

// Liquid wave animation
export const liquidWave = {
  animate: {
    d: [
      "M0,100 C150,200 350,0 500,100 L500,00 L0,0 Z",
      "M0,100 C150,0 350,200 500,100 L500,00 L0,0 Z",
      "M0,100 C150,200 350,0 500,100 L500,00 L0,0 Z"
    ],
    transition: {
      duration: 4,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// Neon glow pulse
export const neonGlow = {
  animate: {
    boxShadow: [
      "0 0 5px #00ff88, 0 0 10px #00ff88, 0 0 15px #00ff88",
      "0 0 10px #00ff88, 0 0 20px #00ff88, 0 0 30px #00ff88",
      "0 0 5px #00ff88, 0 0 10px #00ff88, 0 0 15px #00ff88"
    ],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// Matrix rain effect
export const matrixRain = {
  animate: {
    y: ["-100vh", "100vh"],
    opacity: [0, 1, 0],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "linear",
      staggerChildren: 0.1
    }
  }
};

// Holographic shimmer
export const holographicShimmer = {
  animate: {
    background: [
      "linear-gradient(45deg, #ff0080, #ff8c00, #40e0d0)",
      "linear-gradient(45deg, #40e0d0, #ff0080, #ff8c00)",
      "linear-gradient(45deg, #ff8c00, #40e0d0, #ff0080)",
      "linear-gradient(45deg, #ff0080, #ff8c00, #40e0d0)"
    ],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "linear"
    }
  }
};

// Cyberpunk glitch text
export const cyberpunkGlitch = {
  animate: {
    textShadow: [
      "2px 0 #ff0000, -2px 0 #00ffff",
      "3px 0 #ff0000, -3px 0 #00ffff"
    ],
    x: [0, 1],
    transition: {
      duration: 0.3,
      repeat: Infinity,
      repeatType: "reverse" as const
    }
  }
};

// Floating particles
export const floatingParticles = {
  animate: {
    y: [0, -20],
    x: [0, 10],
    rotate: [0, 360],
    scale: [1, 1.1],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut",
      staggerChildren: 0.2
    }
  }
};

// DNA helix rotation
export const dnaHelix = {
  animate: {
    rotateY: [0, 360],
    rotateX: [0, 180, 360],
    transition: {
      duration: 8,
      repeat: Infinity,
      ease: "linear"
    }
  }
};

// Quantum tunnel effect
export const quantumTunnel = {
  animate: {
    scale: [1, 1.5, 1],
    opacity: [1, 0.3, 1],
    filter: [
      "blur(0px) hue-rotate(0deg)",
      "blur(2px) hue-rotate(180deg)",
      "blur(0px) hue-rotate(360deg)"
    ],
    transition: {
      duration: 4,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};
