import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import OpenAI from 'openai/index.mjs';
import { z } from 'zod';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const chatSchema = z.object({
  message: z.string().min(1, 'Message is required'),
  context: z.object({
    type: z.enum(['general', 'learning_path', 'assignment_help', 'study_tips']).optional(),
    subject: z.string().optional(),
    difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    currentTodos: z.array(z.unknown()).optional(),
    sessionId: z.string().optional(),
    userProfile: z.object({
      role: z.string(),
      subjects: z.array(z.string()).optional(),
      learningGoals: z.array(z.string()).optional(),
    }).optional(),
  }).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { message, context } = chatSchema.parse(body);

    // Try backend chatbot service first
    try {
      const backendResponse = await fetch(`${process.env.BACKEND_URL || 'http://localhost:3001'}/api/chatbot/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.user.id}` // Simplified for demo
        },
        body: JSON.stringify({
          message,
          sessionId: context?.sessionId || null
        })
      });

      if (backendResponse.ok) {
        const data = await backendResponse.json();
        if (data.success) {
          return NextResponse.json({
            success: true,
            data: {
              message: data.response,
              agentType: data.agentType || 'chatbot_moi'
            }
          });
        }
      }
    } catch (backendError) {
      console.log('Backend chatbot unavailable, falling back to OpenAI:', backendError);
    }

    // Fallback to OpenAI if backend is unavailable
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'AI services unavailable' },
        { status: 500 }
      );
    }

    // Build system prompt based on context
    let systemPrompt = `You are an AI tutor for FPT University students. You are helpful, knowledgeable, and encouraging. You specialize in:

1. Creating personalized learning paths
2. Helping with assignments and homework
3. Providing study tips and techniques
4. Breaking down complex topics into manageable steps
5. Generating actionable todo lists for learning goals

User Profile:
- Role: ${session.user.role}
- Name: ${session.user.name}

Guidelines:
- Always be encouraging and supportive
- Provide practical, actionable advice
- Break down complex topics into smaller steps
- Suggest specific resources when helpful
- If creating learning paths, structure them as day-by-day plans
- Focus on FPT University context when relevant`;

    if (context?.type === 'learning_path') {
      systemPrompt += `

You are specifically helping create a personalized learning path. When the user describes their learning goal:
1. Ask clarifying questions about timeline, current knowledge, and specific objectives
2. Create a structured day-by-day learning plan
3. Include specific tasks, resources, and milestones
4. Make it actionable with clear daily goals
5. Suggest a realistic timeline based on the complexity`;
    }

    if (context?.subject) {
      systemPrompt += `\n\nCurrent subject focus: ${context.subject}`;
    }

    if (context?.currentTodos && context.currentTodos.length > 0) {
      systemPrompt += `\n\nUser's current todos: ${JSON.stringify(context.currentTodos.slice(0, 5))}`;
    }

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: message,
        },
      ],
      max_tokens: 1000,
      temperature: 0.7,
    });

    const response = completion.choices[0]?.message?.content;

    if (!response) {
      return NextResponse.json(
        { error: 'No response from AI' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        message: response,
        usage: completion.usage,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error in AI chat:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
