'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { buttonHover } from '@/lib/animations';

const animatedButtonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden',
  {
    variants: {
      variant: {
        default:
          'bg-gradient-to-r from-blue-600 to-purple-600 text-primary-foreground shadow-lg hover:shadow-xl',
        destructive:
          'bg-gradient-to-r from-red-500 to-red-600 text-destructive-foreground shadow-lg hover:shadow-xl',
        outline:
          'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground backdrop-blur-sm',
        secondary:
          'bg-gradient-to-r from-gray-100 to-gray-200 text-secondary-foreground shadow-sm hover:shadow-md',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        gradient:
          'bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 text-white shadow-lg hover:shadow-xl',
        neon:
          'bg-black text-cyan-400 border border-cyan-400 shadow-lg shadow-cyan-400/25 hover:shadow-cyan-400/50',
        glass:
          'bg-white/10 backdrop-blur-md border border-white/20 text-white shadow-lg hover:bg-white/20',
      },
      size: {
        default: 'h-9 px-4 py-2',
        sm: 'h-8 rounded-md px-3 text-xs',
        lg: 'h-11 rounded-md px-8 text-base',
        xl: 'h-14 rounded-lg px-10 text-lg',
        icon: 'h-9 w-9',
      },
      animation: {
        none: '',
        pulse: 'animate-pulse',
        bounce: 'animate-bounce',
        wiggle: 'hover:animate-wiggle',
        glow: 'hover:animate-glow',
        scale: 'hover:scale-105 transition-transform',
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      animation: 'none',
    },
  }
);

export interface AnimatedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof animatedButtonVariants> {
  asChild?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  ripple?: boolean;
}

const AnimatedButton = React.forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    animation,
    asChild = false, 
    loading = false,
    icon,
    rightIcon,
    ripple = true,
    children,
    onClick,
    ...props 
  }, ref) => {
    const [ripples, setRipples] = React.useState<Array<{ id: number; x: number; y: number }>>([]);

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (ripple && !loading) {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const newRipple = { id: Date.now(), x, y };

        setRipples(prev => [...prev, newRipple]);

        setTimeout(() => {
          setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
        }, 600);
      }

      if (onClick) {
        onClick(e);
      }
    };

    if (asChild) {
      return (
        <Slot
          className={cn(animatedButtonVariants({ variant, size, animation, className }))}
          ref={ref}
          onClick={handleClick}
          {...props}
        >
          {children}
        </Slot>
      );
    }

    // Filter out conflicting props
    const { onDrag, onDragEnd, onDragStart, onAnimationStart, onAnimationEnd, ...motionProps } = props as any;

    return (
      <motion.button
        className={cn(animatedButtonVariants({ variant, size, animation, className }))}
        ref={ref}
        onClick={handleClick}
        disabled={loading}
        {...buttonHover}
        {...motionProps}
      >
        {/* Ripple Effect */}
        {ripples.map((ripple) => (
          <motion.span
            key={ripple.id}
            className="absolute rounded-full bg-white/30 pointer-events-none"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          />
        ))}

        {/* Loading Spinner */}
        {loading && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center bg-current/10 rounded-md"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
          </motion.div>
        )}

        {/* Content */}
        <motion.div 
          className={cn("flex items-center gap-2", loading && "opacity-0")}
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: loading ? 0 : 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {icon && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
            >
              {icon}
            </motion.span>
          )}
          {children}
          {rightIcon && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              {rightIcon}
            </motion.span>
          )}
        </motion.div>

        {/* Gradient Overlay for Gradient Variant */}
        {variant === 'gradient' && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-pink-600 via-red-600 to-yellow-600 opacity-0 rounded-md"
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          />
        )}

        {/* Neon Glow Effect */}
        {variant === 'neon' && (
          <motion.div
            className="absolute inset-0 bg-cyan-400/20 rounded-md opacity-0"
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          />
        )}
      </motion.button>
    );
  }
);

AnimatedButton.displayName = 'AnimatedButton';

export { AnimatedButton, animatedButtonVariants };
