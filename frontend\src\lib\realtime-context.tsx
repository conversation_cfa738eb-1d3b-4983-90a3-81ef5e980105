'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuth } from './auth-context';
import { wsClient, useWebSocket } from './websocket';
import { motion, AnimatePresence } from 'framer-motion';
import { Wifi, WifiOff } from 'lucide-react';

interface RealtimeContextType {
  isConnected: boolean;
  onlineUsers: Set<string>;
  notifications: any[];
  dashboardData: any;
  todos: any[];
  classes: any[];
  assignments: any[];
}

const RealtimeContext = createContext<RealtimeContextType | undefined>(undefined);

interface RealtimeProviderProps {
  children: ReactNode;
}

export function RealtimeProvider({ children }: RealtimeProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const ws = useWebSocket();
  
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const [notifications, setNotifications] = useState<any[]>([]);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [todos, setTodos] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [assignments, setAssignments] = useState<any[]>([]);

  useEffect(() => {
    if (isAuthenticated && user) {
      // Connect to WebSocket
      ws.connect(user.id);
      
      // Set up event listeners
      const handleConnect = () => setIsConnected(true);
      const handleDisconnect = () => setIsConnected(false);
      
      const handleNotification = (data: any) => {
        setNotifications(prev => [data, ...prev.slice(0, 49)]); // Keep last 50
      };

      const handleTodoUpdate = (data: any) => {
        setTodos(prev => {
          const index = prev.findIndex(todo => todo.id === data.id);
          if (index >= 0) {
            const updated = [...prev];
            updated[index] = { ...updated[index], ...data };
            return updated;
          }
          return data.action === 'create' ? [data, ...prev] : prev;
        });
      };

      const handleClassUpdate = (data: any) => {
        setClasses(prev => {
          const index = prev.findIndex(cls => cls.id === data.id);
          if (index >= 0) {
            const updated = [...prev];
            updated[index] = { ...updated[index], ...data };
            return updated;
          }
          return data.action === 'create' ? [data, ...prev] : prev;
        });
      };

      const handleAssignmentUpdate = (data: any) => {
        setAssignments(prev => {
          const index = prev.findIndex(assignment => assignment.id === data.id);
          if (index >= 0) {
            const updated = [...prev];
            updated[index] = { ...updated[index], ...data };
            return updated;
          }
          return data.action === 'create' ? [data, ...prev] : prev;
        });
      };

      const handleDashboardUpdate = (data: any) => {
        setDashboardData(data);
      };

      const handleUserOnline = (...args: unknown[]) => {
        const data = args[0] as { userId: string; status: 'online' | 'offline' };
        setOnlineUsers(prev => {
          const newSet = new Set(prev);
          if (data.status === 'online') {
            newSet.add(data.userId);
          } else {
            newSet.delete(data.userId);
          }
          return newSet;
        });
      };

      // Register event listeners
      ws.on('connect', handleConnect);
      ws.on('disconnect', handleDisconnect);
      ws.on('notification', handleNotification);
      ws.on('todoUpdate', handleTodoUpdate);
      ws.on('classUpdate', handleClassUpdate);
      ws.on('assignmentUpdate', handleAssignmentUpdate);
      ws.on('dashboardUpdate', handleDashboardUpdate);
      ws.on('userOnline', handleUserOnline);

      // Join user's personal room
      ws.joinRoom(`user:${user.id}`);

      // Update user status to online
      ws.updateStatus('online');

      // Cleanup on unmount
      return () => {
        ws.off('connect', handleConnect);
        ws.off('disconnect', handleDisconnect);
        ws.off('notification', handleNotification);
        ws.off('todoUpdate', handleTodoUpdate);
        ws.off('classUpdate', handleClassUpdate);
        ws.off('assignmentUpdate', handleAssignmentUpdate);
        ws.off('dashboardUpdate', handleDashboardUpdate);
        ws.off('userOnline', handleUserOnline);
        
        ws.updateStatus('offline');
        ws.leaveRoom(`user:${user.id}`);
      };
    }
  }, [isAuthenticated, user, ws]);

  // Handle page visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        ws.updateStatus('offline');
      } else {
        ws.updateStatus('online');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [ws]);

  const value: RealtimeContextType = {
    isConnected,
    onlineUsers,
    notifications,
    dashboardData,
    todos,
    classes,
    assignments,
  };

  return (
    <RealtimeContext.Provider value={value}>
      {children}
      
      {/* Connection Status Indicator */}
      <AnimatePresence>
        {isAuthenticated && (
          <motion.div
            className="fixed bottom-4 right-4 z-50"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <motion.div
              className={`flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium shadow-lg backdrop-blur-sm ${
                isConnected
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'bg-red-100 text-red-800 border border-red-200'
              }`}
              animate={{
                scale: isConnected ? [1, 1.05, 1] : [1, 0.95, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            >
              <motion.div
                animate={{ rotate: isConnected ? 0 : 360 }}
                transition={{ duration: 1, repeat: isConnected ? 0 : Infinity }}
              >
                {isConnected ? (
                  <Wifi className="h-4 w-4" />
                ) : (
                  <WifiOff className="h-4 w-4" />
                )}
              </motion.div>
              <span>
                {isConnected ? 'Connected' : 'Connecting...'}
              </span>
              
              {/* Online indicator dot */}
              {isConnected && (
                <motion.div
                  className="w-2 h-2 bg-green-500 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [1, 0.5, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                />
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </RealtimeContext.Provider>
  );
}

export function useRealtime() {
  const context = useContext(RealtimeContext);
  if (context === undefined) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
}

export default RealtimeProvider;
