'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Header } from '@/components/layout/header';
import { BlockEditor } from '@/components/editor/block-editor';
import { AnimatedButton } from '@/components/ui/animated-button';
import { Animated<PERSON><PERSON>, Animated<PERSON>ardContent, AnimatedCardHeader, AnimatedCardTitle } from '@/components/ui/animated-card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { AuroraBackground } from '@/components/effects/background-effects';
import { FloatingParticles } from '@/components/effects/particle-system';
import {
  Save,
  Share,
  Download,
  Eye,
  FileText,
  Sparkles,
  Layers,
  Palette,
  Zap
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { pagesApi } from '@/lib/api-client';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';

export default function EditorPage() {
  const [pageTitle, setPageTitle] = useState('Untitled Document');
  const [isPublic, setIsPublic] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [blocks, setBlocks] = useState<any[]>([]);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const pageData = {
        title: pageTitle,
        content: JSON.stringify(blocks),
        isPublic,
        workspaceId: 'default' // You would get this from context
      };

      await pagesApi.create(pageData);
      setLastSaved(new Date());
      toast.success('Document saved successfully!');
    } catch (error) {
      toast.error('Failed to save document');
    } finally {
      setIsSaving(false);
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: pageTitle,
        text: 'Check out this document I created with FPT UniHub',
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  const handleExport = async () => {
    try {
      // This would call the export API
      toast.success('Document exported successfully!');
    } catch (error) {
      toast.error('Failed to export document');
    }
  };

  return (
    <div className="min-h-screen relative">
      {/* Background Effects */}
      <AuroraBackground intensity="low" />
      <FloatingParticles particleCount={20} colors={['#3b82f6', '#8b5cf6']} />
      
      <Header />

      {/* Editor Header */}
      <motion.div
        className="border-b bg-white/80 backdrop-blur-sm sticky top-16 z-40"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <motion.div
                className="flex items-center space-x-2"
                variants={staggerContainer}
                initial="initial"
                animate="animate"
              >
                <motion.div variants={staggerItem}>
                  <FileText className="h-6 w-6 text-blue-600" />
                </motion.div>
                <motion.div variants={staggerItem} className="flex-1">
                  <Input
                    value={pageTitle}
                    onChange={(e) => setPageTitle(e.target.value)}
                    className="text-lg font-semibold border-none bg-transparent focus:bg-white/50 transition-colors"
                    placeholder="Document title..."
                  />
                </motion.div>
              </motion.div>

              <motion.div
                className="flex items-center space-x-2"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
              >
                <Badge variant={isPublic ? "default" : "secondary"}>
                  {isPublic ? "Public" : "Private"}
                </Badge>
                {lastSaved && (
                  <span className="text-xs text-muted-foreground">
                    Saved {lastSaved.toLocaleTimeString()}
                  </span>
                )}
              </motion.div>
            </div>

            <motion.div
              className="flex items-center space-x-2"
              variants={staggerContainer}
              initial="initial"
              animate="animate"
            >
              <motion.div variants={staggerItem}>
                <AnimatedButton
                  variant="outline"
                  size="sm"
                  onClick={() => setIsPublic(!isPublic)}
                  animation="scale"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {isPublic ? "Make Private" : "Make Public"}
                </AnimatedButton>
              </motion.div>

              <motion.div variants={staggerItem}>
                <AnimatedButton
                  variant="outline"
                  size="sm"
                  onClick={handleShare}
                  animation="scale"
                >
                  <Share className="h-4 w-4 mr-2" />
                  Share
                </AnimatedButton>
              </motion.div>

              <motion.div variants={staggerItem}>
                <AnimatedButton
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                  animation="scale"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </AnimatedButton>
              </motion.div>

              <motion.div variants={staggerItem}>
                <AnimatedButton
                  onClick={handleSave}
                  loading={isSaving}
                  animation="pulse"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </AnimatedButton>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Editor Content */}
      <motion.div
        className="container mx-auto px-4 py-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.6 }}
      >
        <div className="max-w-4xl mx-auto">
          {/* Editor Features Showcase */}
          <motion.div
            className="mb-8 grid md:grid-cols-3 gap-4"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            <motion.div variants={staggerItem}>
              <AnimatedCard className="text-center p-4 bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
                <AnimatedCardContent>
                  <Layers className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">14+ Block Types</h3>
                  <p className="text-xs text-muted-foreground">Rich content blocks</p>
                </AnimatedCardContent>
              </AnimatedCard>
            </motion.div>

            <motion.div variants={staggerItem}>
              <AnimatedCard className="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
                <AnimatedCardContent>
                  <Palette className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Beautiful Design</h3>
                  <p className="text-xs text-muted-foreground">Modern interface</p>
                </AnimatedCardContent>
              </AnimatedCard>
            </motion.div>

            <motion.div variants={staggerItem}>
              <AnimatedCard className="text-center p-4 bg-gradient-to-br from-green-50 to-blue-50 border-green-200">
                <AnimatedCardContent>
                  <Zap className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Real-time Sync</h3>
                  <p className="text-xs text-muted-foreground">Live collaboration</p>
                </AnimatedCardContent>
              </AnimatedCard>
            </motion.div>
          </motion.div>

          {/* Block Editor */}
          <motion.div
            className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            <div className="p-6">
              <BlockEditor
                pageId="demo-page"
                onSave={(newBlocks) => setBlocks(newBlocks)}
              />
            </div>
          </motion.div>

          {/* Tips */}
          <motion.div
            className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
          >
            <div className="flex items-start space-x-3">
              <Sparkles className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">Pro Tips</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Press <kbd className="px-1 py-0.5 bg-white rounded text-xs">Enter</kbd> to create a new block</li>
                  <li>• Use <kbd className="px-1 py-0.5 bg-white rounded text-xs">+</kbd> button to add different block types</li>
                  <li>• Drag blocks to reorder them</li>
                  <li>• Type <kbd className="px-1 py-0.5 bg-white rounded text-xs">/</kbd> for quick commands</li>
                </ul>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
