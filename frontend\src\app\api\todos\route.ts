import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { TodoStatus, Priority, TodoSource } from '@prisma/client';

const createTodoSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200),
  description: z.string().optional(),
  deadline: z.string().datetime().optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  source: z.enum(['TEACHER_ASSIGNMENT', 'AI_LEARNING_PATH', 'GROUP_TASK', 'PERSONAL']).default('PERSONAL'),
  assignmentId: z.string().cuid().optional(),
  aiPathId: z.string().cuid().optional(),
  groupId: z.string().cuid().optional(),
});

const updateTodoSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().optional(),
  deadline: z.string().datetime().optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'OVERDUE']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  order: z.number().optional(),
});

// GET /api/todos - Get user's todos
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const source = searchParams.get('source');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    const whereClause: Record<string, unknown> = {
      userId: session.user.id,
    };

    if (status) {
      whereClause.status = status;
    }

    if (priority) {
      whereClause.priority = priority;
    }

    if (source) {
      whereClause.source = source;
    }

    if (search) {
      whereClause.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [todos, total] = await Promise.all([
      prisma.todo.findMany({
        where: whereClause,
        include: {
          assignment: {
            include: {
              class: {
                select: { id: true, name: true, subject: true }
              }
            }
          },
          aiPath: {
            select: { id: true, title: true, subject: true }
          },
          group: {
            select: { id: true, name: true }
          }
        },
        orderBy: [
          { order: 'asc' },
          { deadline: 'asc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      prisma.todo.count({ where: whereClause }),
    ]);

    // Update overdue todos
    const now = new Date();
    const overdueTodos = todos.filter((todo: any) =>
      todo.deadline &&
      todo.deadline < now &&
      todo.status !== 'COMPLETED' &&
      todo.status !== 'OVERDUE'
    );

    if (overdueTodos.length > 0) {
      await prisma.todo.updateMany({
        where: {
          id: { in: overdueTodos.map((t: any) => t.id) }
        },
        data: { status: 'OVERDUE' }
      });

      // Update the todos in response
      overdueTodos.forEach((todo: any) => {
        todo.status = 'OVERDUE';
      });
    }

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: todos,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching todos:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/todos - Create todo
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createTodoSchema.parse(body);

    // Get the highest order for user's todos
    const lastTodo = await prisma.todo.findFirst({
      where: { userId: session.user.id },
      orderBy: { order: 'desc' },
      select: { order: true }
    });

    const newOrder = (lastTodo?.order || 0) + 1;

    const todo = await prisma.todo.create({
      data: {
        title: validatedData.title,
        description: validatedData.description,
        deadline: validatedData.deadline ? new Date(validatedData.deadline) : null,
        priority: validatedData.priority,
        source: validatedData.source,
        userId: session.user.id,
        assignmentId: validatedData.assignmentId,
        aiPathId: validatedData.aiPathId,
        groupId: validatedData.groupId,
        order: newOrder,
      },
      include: {
        assignment: {
          include: {
            class: {
              select: { id: true, name: true, subject: true }
            }
          }
        },
        aiPath: {
          select: { id: true, title: true, subject: true }
        },
        group: {
          select: { id: true, name: true }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: todo,
      message: 'Todo created successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating todo:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH /api/todos/reorder - Reorder todos
export async function PATCH(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { todoIds } = z.object({
      todoIds: z.array(z.string().cuid())
    }).parse(body);

    // Update order for each todo
    const updatePromises = todoIds.map((todoId, index) =>
      prisma.todo.updateMany({
        where: {
          id: todoId,
          userId: session.user.id, // Ensure user owns the todo
        },
        data: { order: index }
      })
    );

    await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      message: 'Todos reordered successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error reordering todos:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
