Metadata-Version: 2.2
Name: psycopg-pool
Version: 3.2.6
Summary: Connection Pool for Psycopg
Home-page: https://psycopg.org/psycopg3/
Author: <PERSON><PERSON>
Author-email: danie<PERSON>.<EMAIL>
License: GNU Lesser General Public License v3 (LGPLv3)
Project-URL: Homepage, https://psycopg.org/
Project-URL: Documentation, https://www.psycopg.org/psycopg3/docs/advanced/pool.html
Project-URL: Changes, https://psycopg.org/psycopg3/docs/news_pool.html
Project-URL: Code, https://github.com/psycopg/psycopg
Project-URL: Issue Tracker, https://github.com/psycopg/psycopg/issues
Project-URL: Download, https://pypi.org/project/psycopg-pool/
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Database
Classifier: Topic :: Database :: Front-Ends
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Requires-Dist: typing-extensions>=4.6

Psycopg 3: PostgreSQL database adapter for Python - Connection Pool
===================================================================

This distribution package is an optional component of `Psycopg 3`__: it
contains the optional connection pool package `psycopg_pool`__.

.. __: https://pypi.org/project/psycopg/
.. __: https://www.psycopg.org/psycopg3/docs/advanced/pool.html

This package is kept separate from the main ``psycopg`` package because it is
likely that it will follow a different release cycle.

You can also install this package using::

    pip install "psycopg[pool]"

Please read `the project readme`__ and `the installation documentation`__ for
more details.

.. __: https://github.com/psycopg/psycopg#readme
.. __: https://www.psycopg.org/psycopg3/docs/basic/install.html
       #installing-the-connection-pool


Copyright (C) 2020 The Psycopg Team
