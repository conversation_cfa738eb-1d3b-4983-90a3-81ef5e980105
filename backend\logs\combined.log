{"level":"info","message":"::1 - - [05/Aug/2025:18:48:47 +0000] \"GET /health HTTP/1.1\" 200 237 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"fpt-unihub-backend","timestamp":"2025-08-05T18:48:47.205Z"}
{"duration":"751ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"fpt-unihub-backend","status":200,"timestamp":"2025-08-05T18:48:47.467Z","url":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"level":"info","message":"::1 - - [05/Aug/2025:18:48:49 +0000] \"GET /favicon.ico HTTP/1.1\" 500 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"fpt-unihub-backend","timestamp":"2025-08-05T18:48:49.375Z"}
{"duration":"445ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T18:48:49.384Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
