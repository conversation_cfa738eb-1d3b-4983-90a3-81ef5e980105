import NextAuth from 'next-auth';
import { PrismaAdapter } from '@auth/prisma-adapter';
import Google from 'next-auth/providers/google';
import Credentials from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import { prisma } from './prisma';
import { Role } from '@prisma/client';
import { NextRequest } from 'next/server';

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    Credentials({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials: any) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Missing credentials');
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        });

        if (!user) {
          throw new Error('User not found');
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password || ''
        );

        if (!isPasswordValid) {
          throw new Error('Invalid password');
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.avatar,
          role: user.role,
        };
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user }: { token: any; user: any }) {
      if (user) {
        token.role = user.role;
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }: { session: any; token: any }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as Role;
      }
      return session;
    },
    async signIn({ user, account }: { user: any; account: any }) {
      if (account?.provider === 'google') {
        try {
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! },
          });

          if (!existingUser) {
            await prisma.user.create({
              data: {
                email: user.email!,
                name: user.name!,
                avatar: user.image,
                role: Role.STUDENT,
              },
            });
          }
          return true;
        } catch (error) {
          console.error('Error during sign in:', error);
          return false;
        }
      }
      return true;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  debug: process.env.NODE_ENV === 'development',
  logger: {
    error(code: any, metadata: any) {
      console.error('[NextAuth Error]', code, metadata);
    },
    warn(code: any) {
      console.warn('[NextAuth Warning]', code);
    },
    debug(code: any, metadata: any) {
      console.log('[NextAuth Debug]', code, metadata);
    },
  },
});

// Helper function for getting session in API routes (NextAuth.js v5)
export async function getServerSession() {
  return await auth();
}

// For backward compatibility
export const authOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    Credentials({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials: any) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Missing credentials');
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        });

        if (!user) {
          throw new Error('User not found');
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password || ''
        );

        if (!isPasswordValid) {
          throw new Error('Invalid password');
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.avatar,
          role: user.role,
        };
      },
    }),
  ],
  session: {
    strategy: 'jwt' as const,
  },
  callbacks: {
    async jwt({ token, user }: { token: any; user: any }) {
      if (user) {
        token.role = user.role;
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }: { session: any; token: any }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as Role;
      }
      return session;
    },
    async signIn({ user, account }: { user: any; account: any }) {
      if (account?.provider === 'google') {
        try {
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! },
          });

          if (!existingUser) {
            await prisma.user.create({
              data: {
                email: user.email!,
                name: user.name!,
                avatar: user.image,
                role: Role.STUDENT,
              },
            });
          }
          return true;
        } catch (error) {
          console.error('Error during sign in:', error);
          return false;
        }
      }
      return true;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
};

// Helper functions for role-based access control
export function hasRole(userRole: Role, requiredRoles: Role[]): boolean {
  return requiredRoles.includes(userRole);
}

export function isAdmin(userRole: Role): boolean {
  return userRole === Role.ADMIN;
}

export function isTeacher(userRole: Role): boolean {
  return userRole === Role.TEACHER || userRole === Role.ADMIN;
}

export function isStudent(userRole: Role): boolean {
  return userRole === Role.STUDENT;
}

export function canAccessClass(userRole: Role, userId: string, classData: { teacherId: string; members?: { userId: string }[] }): boolean {
  // Admin can access all classes
  if (userRole === Role.ADMIN) return true;
  
  // Teacher can access their own classes
  if (userRole === Role.TEACHER && classData.teacherId === userId) return true;
  
  // Students can access classes they're enrolled in
  if (userRole === Role.STUDENT) {
    return classData.members?.some((member) => member.userId === userId) || false;
  }
  
  return false;
}

export function canManageAssignment(userRole: Role, userId: string, assignmentData: { teacherId: string }): boolean {
  // Admin can manage all assignments
  if (userRole === Role.ADMIN) return true;
  
  // Teacher can manage their own assignments
  if (userRole === Role.TEACHER && assignmentData.teacherId === userId) return true;
  
  return false;
}

export function canAccessTodo(userRole: Role, userId: string, todoData: { userId: string; assignment?: { teacherId: string } }): boolean {
  // Admin can access all todos
  if (userRole === Role.ADMIN) return true;
  
  // Users can access their own todos
  if (todoData.userId === userId) return true;
  
  // Teachers can access todos related to their assignments
  if (userRole === Role.TEACHER && todoData.assignment?.teacherId === userId) return true;
  
  return false;
}
