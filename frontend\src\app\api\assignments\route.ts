import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { Role } from '@prisma/client';

const createAssignmentSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200),
  description: z.string().min(1, 'Description is required'),
  deadline: z.string().datetime(),
  classId: z.string().cuid(),
  attachments: z.array(z.string().url()).optional().default([]),
});

// GET /api/assignments - Get assignments
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const classId = searchParams.get('classId');
    const status = searchParams.get('status');

    const skip = (page - 1) * limit;

    const whereClause: Record<string, unknown> = {};

    if (session.user.role === Role.TEACHER || session.user.role === Role.ADMIN) {
      // Teachers see their own assignments
      whereClause.teacherId = session.user.id;
    } else {
      // Students see assignments from their enrolled classes
      whereClause.class = {
        members: {
          some: { userId: session.user.id }
        }
      };
    }

    if (classId) {
      whereClause.classId = classId;
    }

    if (status) {
      whereClause.status = status;
    }

    const [assignments, total] = await Promise.all([
      prisma.assignment.findMany({
        where: whereClause,
        include: {
          class: {
            select: { id: true, name: true, subject: true }
          },
          teacher: {
            select: { id: true, name: true, email: true, avatar: true }
          },
          _count: {
            select: { todos: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.assignment.count({ where: whereClause }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: assignments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching assignments:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/assignments - Create assignment (Teachers only)
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only teachers and admins can create assignments
    if (session.user.role !== Role.TEACHER && session.user.role !== Role.ADMIN) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createAssignmentSchema.parse(body);

    // Verify teacher owns the class
    const classData = await prisma.class.findUnique({
      where: { id: validatedData.classId },
      include: {
        members: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      }
    });

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 });
    }

    if (classData.teacherId !== session.user.id && session.user.role !== Role.ADMIN) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Create assignment
    const assignment = await prisma.assignment.create({
      data: {
        title: validatedData.title,
        description: validatedData.description,
        deadline: new Date(validatedData.deadline),
        attachments: validatedData.attachments,
        classId: validatedData.classId,
        teacherId: session.user.id,
      },
      include: {
        class: {
          select: { id: true, name: true, subject: true }
        },
        teacher: {
          select: { id: true, name: true, email: true, avatar: true }
        }
      }
    });

    // Auto-generate todos for all class members
    const todoPromises = classData.members.map((member: { userId: string }) =>
      prisma.todo.create({
        data: {
          title: assignment.title,
          description: assignment.description,
          deadline: assignment.deadline,
          status: 'PENDING',
          priority: 'MEDIUM',
          source: 'TEACHER_ASSIGNMENT',
          userId: member.userId,
          assignmentId: assignment.id,
        }
      })
    );

    await Promise.all(todoPromises);

    // Create notifications for all class members
    const notificationPromises = classData.members.map((member: { userId: string }) =>
      prisma.notification.create({
        data: {
          title: 'New Assignment',
          message: `You have a new assignment: ${assignment.title}`,
          type: 'ASSIGNMENT',
          userId: member.userId,
          data: {
            assignmentId: assignment.id,
            classId: assignment.classId,
          },
        }
      })
    );

    await Promise.all(notificationPromises);

    return NextResponse.json({
      success: true,
      data: assignment,
      message: 'Assignment created successfully and todos generated for all students',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating assignment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
