'use client';

import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useMounted } from '@/hooks/use-mounted';

interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  color: string;
  opacity: number;
  life: number;
  maxLife: number;
}

interface ParticleSystemProps {
  particleCount?: number;
  colors?: string[];
  className?: string;
  interactive?: boolean;
  type?: 'floating' | 'explosion' | 'matrix' | 'stars' | 'bubbles';
}

export function ParticleSystem({
  particleCount = 30,
  colors = ['#3b82f6', '#8b5cf6', '#06b6d4'],
  className = '',
  interactive = false,
  type = 'floating'
}: ParticleSystemProps) {
  const mounted = useMounted();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationRef = useRef<number | undefined>(undefined);
  const mouseRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    if (!mounted) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize particles
    const initParticles = () => {
      particlesRef.current = [];
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push(createParticle(i, canvas.width, canvas.height));
      }
    };

    const createParticle = (id: number, width: number, height: number): Particle => {
      // Use deterministic values based on id to avoid hydration mismatch
      const seed = id * 0.618033988749; // Golden ratio for better distribution
      const x = ((seed * 1000) % 1) * width;
      const y = ((seed * 1337) % 1) * height;
      const colorIndex = Math.floor(((seed * 2000) % 1) * colors.length);
      const opacity = ((seed * 3000) % 1) * 0.6 + 0.4;
      const maxLife = ((seed * 4000) % 1) * 100 + 150;

      const baseConfig = {
        id,
        x,
        y,
        color: colors[colorIndex],
        opacity,
        life: 0,
        maxLife
      };

      switch (type) {
        case 'floating':
          return {
            ...baseConfig,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 3 + 1
          };
        case 'explosion':
          return {
            ...baseConfig,
            x: width / 2,
            y: height / 2,
            vx: (Math.random() - 0.5) * 8,
            vy: (Math.random() - 0.5) * 8,
            size: Math.random() * 5 + 2
          };
        case 'matrix':
          return {
            ...baseConfig,
            x: Math.random() * width,
            y: -10,
            vx: 0,
            vy: Math.random() * 3 + 1,
            size: Math.random() * 2 + 1
          };
        case 'stars':
          return {
            ...baseConfig,
            vx: 0,
            vy: 0,
            size: Math.random() * 2 + 0.5
          };
        case 'bubbles':
          return {
            ...baseConfig,
            y: height + 10,
            vx: (Math.random() - 0.5) * 1,
            vy: -(Math.random() * 2 + 1),
            size: Math.random() * 8 + 3
          };
        default:
          return {
            ...baseConfig,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 3 + 1
          };
      }
    };

    const updateParticle = (particle: Particle, width: number, height: number) => {
      particle.life++;
      
      // Update position
      particle.x += particle.vx;
      particle.y += particle.vy;

      // Interactive mouse effect
      if (interactive) {
        const dx = mouseRef.current.x - particle.x;
        const dy = mouseRef.current.y - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 100) {
          const force = (100 - distance) / 100;
          particle.vx += (dx / distance) * force * 0.01;
          particle.vy += (dy / distance) * force * 0.01;
        }
      }

      // Type-specific behavior
      switch (type) {
        case 'floating':
          // Gentle floating motion
          particle.vx += (Math.random() - 0.5) * 0.01;
          particle.vy += (Math.random() - 0.5) * 0.01;
          particle.vx *= 0.99;
          particle.vy *= 0.99;
          break;
        case 'explosion':
          // Gravity and friction
          particle.vy += 0.02;
          particle.vx *= 0.99;
          particle.vy *= 0.99;
          break;
        case 'matrix':
          // Falling effect
          if (particle.y > height) {
            particle.y = -10;
            particle.x = Math.random() * width;
          }
          break;
        case 'stars':
          // Twinkling effect
          particle.opacity = 0.3 + Math.sin(particle.life * 0.05) * 0.7;
          break;
        case 'bubbles':
          // Rising bubbles
          if (particle.y < -10) {
            particle.y = height + 10;
            particle.x = Math.random() * width;
          }
          break;
      }

      // Boundary checks
      if (type !== 'matrix' && type !== 'bubbles') {
        if (particle.x < 0 || particle.x > width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > height) particle.vy *= -1;
        
        particle.x = Math.max(0, Math.min(width, particle.x));
        particle.y = Math.max(0, Math.min(height, particle.y));
      }

      // Reset particle if life exceeded
      if (particle.life > particle.maxLife) {
        const newParticle = createParticle(particle.id, width, height);
        Object.assign(particle, newParticle);
      }
    };

    const drawParticle = (particle: Particle) => {
      ctx.save();
      ctx.globalAlpha = particle.opacity;
      
      // Create gradient for more visual appeal
      const gradient = ctx.createRadialGradient(
        particle.x, particle.y, 0,
        particle.x, particle.y, particle.size
      );
      gradient.addColorStop(0, particle.color);
      gradient.addColorStop(1, 'transparent');
      
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      ctx.fill();
      
      // Add glow effect for certain types
      if (type === 'stars' || type === 'explosion') {
        ctx.shadowBlur = particle.size * 2;
        ctx.shadowColor = particle.color;
        ctx.fill();
      }
      
      ctx.restore();
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particlesRef.current.forEach(particle => {
        updateParticle(particle, canvas.width, canvas.height);
        drawParticle(particle);
      });
      
      // Draw connections for floating type
      if (type === 'floating') {
        drawConnections();
      }
      
      animationRef.current = requestAnimationFrame(animate);
    };

    const drawConnections = () => {
      ctx.save();
      ctx.strokeStyle = colors[0];
      ctx.lineWidth = 0.5;
      ctx.globalAlpha = 0.3;
      
      for (let i = 0; i < particlesRef.current.length; i++) {
        for (let j = i + 1; j < particlesRef.current.length; j++) {
          const p1 = particlesRef.current[i];
          const p2 = particlesRef.current[j];
          const distance = Math.sqrt((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2);
          
          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.stroke();
          }
        }
      }
      ctx.restore();
    };

    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouseRef.current.x = e.clientX - rect.left;
      mouseRef.current.y = e.clientY - rect.top;
    };

    if (interactive) {
      canvas.addEventListener('mousemove', handleMouseMove);
    }

    initParticles();
    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (interactive) {
        canvas.removeEventListener('mousemove', handleMouseMove);
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [mounted, particleCount, colors, interactive, type]);

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{ zIndex: -1 }}
    />
  );
}

// Preset particle effects
export function FloatingParticles(props: Omit<ParticleSystemProps, 'type'>) {
  return <ParticleSystem {...props} type="floating" />;
}

export function ExplosionParticles(props: Omit<ParticleSystemProps, 'type'>) {
  return <ParticleSystem {...props} type="explosion" />;
}

export function MatrixRain(props: Omit<ParticleSystemProps, 'type'>) {
  return <ParticleSystem {...props} type="matrix" colors={['#00ff00', '#00aa00']} />;
}

export function StarField(props: Omit<ParticleSystemProps, 'type'>) {
  return <ParticleSystem {...props} type="stars" colors={['#ffffff', '#ffff88', '#88ffff']} />;
}

export function BubbleEffect(props: Omit<ParticleSystemProps, 'type'>) {
  return <ParticleSystem {...props} type="bubbles" colors={['#3b82f6', '#06b6d4', '#8b5cf6']} />;
}
