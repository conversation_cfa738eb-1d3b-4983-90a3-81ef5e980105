'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Users, Plus, BookOpen, Calendar, Clock, UserPlus, Settings, Copy, ExternalLink } from 'lucide-react';
import { AnimatedButton } from '@/components/ui/animated-button';
import { <PERSON><PERSON><PERSON>, Animated<PERSON><PERSON>Content, AnimatedCardHeader, AnimatedCardTitle, GlowCard } from '@/components/ui/animated-card';
import { Form, FormInput, FormTextarea } from '@/components/ui/form';
import { useAuth } from '@/lib/auth-context';
import { useRealtime } from '@/lib/realtime-context';
import { classesApi } from '@/lib/api-client';
import { toast } from 'react-hot-toast';
import { cn } from '@/lib/utils';
import * as z from 'zod';

const classSchema = z.object({
  name: z.string().min(1, 'Class name is required'),
  description: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
});

interface Class {
  id: string;
  name: string;
  description?: string;
  subject: string;
  inviteCode: string;
  teacherId: string;
  teacher: {
    name: string;
    email: string;
  };
  _count: {
    members: number;
    assignments: number;
  };
  createdAt: string;
}

export function ClassManager() {
  const { user, isTeacher } = useAuth();
  const { classes: realtimeClasses } = useRealtime();
  const [classes, setClasses] = useState<Class[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isJoining, setIsJoining] = useState(false);
  const [joinCode, setJoinCode] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchClasses();
  }, []);

  // Update classes when real-time data changes
  useEffect(() => {
    if (realtimeClasses) {
      setClasses(realtimeClasses);
    }
  }, [realtimeClasses]);

  const fetchClasses = async () => {
    try {
      setIsLoading(true);
      const response = await classesApi.getAll();
      if (response.success && response.data) {
        setClasses(response.data as Class[]);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateClass = async (data: z.infer<typeof classSchema>) => {
    try {
      const response = await classesApi.create(data);
      if (response.success) {
        setIsCreating(false);
        // Real-time update will handle adding to list
      }
    } catch (error) {
      console.error('Error creating class:', error);
    }
  };

  const handleJoinClass = async () => {
    if (!joinCode.trim()) {
      toast.error('Please enter an invite code');
      return;
    }

    try {
      const response = await classesApi.join(joinCode);
      if (response.success) {
        setIsJoining(false);
        setJoinCode('');
        toast.success('Successfully joined class!');
        // Real-time update will handle adding to list
      }
    } catch (error) {
      console.error('Error joining class:', error);
    }
  };

  const copyInviteCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success('Invite code copied to clipboard!');
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map(i => (
          <AnimatedCard key={i} className="animate-pulse">
            <AnimatedCardContent className="p-6">
              <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-gray-300 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-2/3"></div>
            </AnimatedCardContent>
          </AnimatedCard>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">My Classes</h2>
          <p className="text-gray-600">Manage your classes and assignments</p>
        </div>
        
        <div className="flex items-center gap-3">
          {!isTeacher && (
            <AnimatedButton
              onClick={() => setIsJoining(true)}
              variant="outline"
              icon={<UserPlus className="h-4 w-4" />}
            >
              Join Class
            </AnimatedButton>
          )}
          
          {isTeacher && (
            <AnimatedButton
              onClick={() => setIsCreating(true)}
              icon={<Plus className="h-4 w-4" />}
            >
              Create Class
            </AnimatedButton>
          )}
        </div>
      </div>

      {/* Create Class Modal */}
      <AnimatePresence>
        {isCreating && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsCreating(false)}
          >
            <motion.div
              className="bg-white rounded-lg p-6 w-full max-w-md"
              initial={{ scale: 0.8, y: 50 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.8, y: 50 }}
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Create New Class</h3>
              
              <Form
                schema={classSchema}
                onSubmit={handleCreateClass}
                className="space-y-4"
              >
                <FormInput
                  name="name"
                  label="Class Name"
                  placeholder="e.g., Advanced React Development"
                  required
                />
                
                <FormInput
                  name="subject"
                  label="Subject"
                  placeholder="e.g., Computer Science"
                  required
                />
                
                <FormTextarea
                  name="description"
                  label="Description"
                  placeholder="Brief description of the class..."
                  rows={3}
                />
                
                <div className="flex justify-end gap-2 pt-4">
                  <AnimatedButton
                    type="button"
                    variant="ghost"
                    onClick={() => setIsCreating(false)}
                  >
                    Cancel
                  </AnimatedButton>
                  <AnimatedButton type="submit">
                    Create Class
                  </AnimatedButton>
                </div>
              </Form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Join Class Modal */}
      <AnimatePresence>
        {isJoining && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsJoining(false)}
          >
            <motion.div
              className="bg-white rounded-lg p-6 w-full max-w-md"
              initial={{ scale: 0.8, y: 50 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.8, y: 50 }}
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Join Class</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Invite Code
                  </label>
                  <input
                    type="text"
                    value={joinCode}
                    onChange={(e) => setJoinCode(e.target.value)}
                    placeholder="Enter class invite code..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div className="flex justify-end gap-2 pt-4">
                  <AnimatedButton
                    variant="ghost"
                    onClick={() => setIsJoining(false)}
                  >
                    Cancel
                  </AnimatedButton>
                  <AnimatedButton onClick={handleJoinClass}>
                    Join Class
                  </AnimatedButton>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Classes Grid */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.1 }}
      >
        <AnimatePresence>
          {classes.map((classItem, index) => (
            <motion.div
              key={classItem.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
            >
              <GlowCard className="h-full group cursor-pointer">
                <AnimatedCardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <motion.div 
                        className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg text-white"
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                      >
                        <BookOpen className="h-5 w-5" />
                      </motion.div>
                      <div>
                        <AnimatedCardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                          {classItem.name}
                        </AnimatedCardTitle>
                        <p className="text-sm text-gray-500">{classItem.subject}</p>
                      </div>
                    </div>
                    
                    {isTeacher && classItem.teacherId === user?.id && (
                      <motion.button
                        className="p-1 text-gray-400 hover:text-gray-600 rounded"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Settings className="h-4 w-4" />
                      </motion.button>
                    )}
                  </div>
                </AnimatedCardHeader>

                <AnimatedCardContent>
                  {classItem.description && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {classItem.description}
                    </p>
                  )}
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="flex items-center gap-1 text-gray-500">
                        <Users className="h-4 w-4" />
                        {classItem._count.members} members
                      </span>
                      <span className="flex items-center gap-1 text-gray-500">
                        <Calendar className="h-4 w-4" />
                        {classItem._count.assignments} assignments
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">
                        Teacher: {classItem.teacher.name}
                      </span>
                      <span className="text-gray-400">
                        {new Date(classItem.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    
                    {isTeacher && classItem.teacherId === user?.id && (
                      <div className="flex items-center gap-2 pt-2 border-t">
                        <code className="flex-1 text-xs bg-gray-100 px-2 py-1 rounded font-mono">
                          {classItem.inviteCode}
                        </code>
                        <motion.button
                          onClick={() => copyInviteCode(classItem.inviteCode)}
                          className="p-1 text-gray-400 hover:text-blue-600 rounded"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Copy className="h-4 w-4" />
                        </motion.button>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex justify-end pt-4">
                    <AnimatedButton
                      size="sm"
                      variant="ghost"
                      rightIcon={<ExternalLink className="h-4 w-4" />}
                    >
                      View Class
                    </AnimatedButton>
                  </div>
                </AnimatedCardContent>
              </GlowCard>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {classes.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <motion.div
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <BookOpen className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          </motion.div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No classes yet</h3>
          <p className="text-gray-500 mb-6">
            {isTeacher 
              ? "Create your first class to get started" 
              : "Join a class using an invite code"}
          </p>
          <AnimatedButton
            onClick={() => isTeacher ? setIsCreating(true) : setIsJoining(true)}
            icon={isTeacher ? <Plus className="h-4 w-4" /> : <UserPlus className="h-4 w-4" />}
          >
            {isTeacher ? "Create Class" : "Join Class"}
          </AnimatedButton>
        </motion.div>
      )}
    </div>
  );
}
