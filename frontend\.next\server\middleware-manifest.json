{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "2Bt72EhKHzAFecad9JICYXURSTZ3AOoAtkPhMpYqSEU=", "__NEXT_PREVIEW_MODE_ID": "4e71e847e8d1f3f44bf4d049389c8da6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "12e608fa3362ce97bf946aea130a19896e62296dd01b6e6ab09e9edf010809b8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a94c5d62fa6cc4dafc8115c35f11f6de18008516dc71e5beb4af2111ec3d2b48"}}}, "functions": {}, "sortedMiddleware": ["/"]}