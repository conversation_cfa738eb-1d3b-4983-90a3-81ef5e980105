const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');

class ChatbotService {
  constructor() {
    this.baseURL = process.env.CHATBOT_API_URL || 'http://localhost:8001';
    this.chatbotProcess = null;
    this.isRunning = false;
  }

  async startChatbot() {
    if (this.isRunning) return;

    try {
      const chatbotPath = path.join(__dirname, '../../chatbot_moi/main.py');
      
      console.log(' Starting chatbot_moi...');
      
      this.chatbotProcess = spawn('python', [chatbotPath], {
        cwd: path.join(__dirname, '../../chatbot_moi'),
        stdio: 'pipe'
      });

      this.chatbotProcess.on('error', (error) => {
        console.error('❌ Failed to start chatbot:', error);
        this.isRunning = false;
      });

      this.chatbotProcess.on('close', (code) => {
        console.log(`🤖 Chatbot process exited with code ${code}`);
        this.isRunning = false;
      });

      // Wait for chatbot to start
      await new Promise((resolve) => {
        const timeout = setTimeout(() => {
          console.warn('⚠️ Chatbot startup timeout - continuing without chatbot');
          this.isRunning = false;
          resolve(); // Don't reject, just continue
        }, 20000); // Increased timeout to 20 seconds

        let startupDetected = false;

        this.chatbotProcess.stdout.on('data', (data) => {
          const output = data.toString();
          console.log('🤖 Chatbot:', output);
          if (!startupDetected && (output.includes('Uvicorn running') || output.includes('Application startup complete') || output.includes('INFO:     Started server process') || output.includes('Uvicorn running on http://0.0.0.0:8000'))) {
            startupDetected = true;
            clearTimeout(timeout);
            this.isRunning = true;
            resolve();
          }
        });

        this.chatbotProcess.stderr.on('data', (data) => {
          const errorOutput = data.toString();
          // Check if stderr contains startup success messages
          if (!startupDetected && (errorOutput.includes('Uvicorn running') || errorOutput.includes('Application startup complete') || errorOutput.includes('INFO:     Started server process') || errorOutput.includes('Uvicorn running on http://0.0.0.0:8000'))) {
            startupDetected = true;
            clearTimeout(timeout);
            this.isRunning = true;
            resolve();
          }
          console.error('🤖 Chatbot Error:', errorOutput);
        });
      });

      if (this.isRunning) {
        console.log('✅ Chatbot started successfully');
      } else {
        console.log('⚠️ Chatbot not available - continuing without it');
      }
    } catch (error) {
      console.error('❌ Failed to start chatbot:', error);
      console.log('⚠️ Continuing without chatbot service');
      this.isRunning = false;
    }
  }

  async stopChatbot() {
    if (this.chatbotProcess) {
      this.chatbotProcess.kill();
      this.isRunning = false;
      console.log('🛑 Chatbot stopped');
    }
  }

  async sendMessage(message, userId = null) {
    try {
      // Ensure chatbot is running
      if (!this.isRunning) {
        await this.startChatbot();
      }

      // If chatbot is still not available, return a fallback response
      if (!this.isRunning) {
        return {
          success: false,
          response: 'Xin lỗi, chatbot hiện không khả dụng. Vui lòng thử lại sau.',
          error: 'Chatbot service not available'
        };
      }

      const response = await axios.post(`${this.baseURL}/chat`, {
        message,
        userId
      }, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        response: response.data.response,
        agentType: response.data.agentType || 'generic_agent'
      };
    } catch (error) {
      console.error('Chatbot API Error:', error.message);
      
      // If connection failed, try to restart chatbot
      if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        console.log('🔄 Attempting to restart chatbot...');
        await this.stopChatbot();
        await this.startChatbot();
        
        // Retry once only if chatbot is now running
        if (this.isRunning) {
          try {
            const retryResponse = await axios.post(`${this.baseURL}/chat`, {
              message,
              userId
            }, {
              timeout: 30000,
              headers: {
                'Content-Type': 'application/json'
              }
            });

            return {
              success: true,
              response: retryResponse.data.response,
              agentType: retryResponse.data.agentType || 'generic_agent'
            };
          } catch (retryError) {
            console.error('Chatbot retry failed:', retryError.message);
          }
        }
      }

      return {
        success: false,
        response: 'Xin lỗi, tôi đang gặp sự cố. Vui lòng thử lại sau.',
        error: error.message
      };
    }
  }

  async getChatHistory(userId, limit = 50) {
    try {
      const response = await axios.get(`${this.baseURL}/chat/history/${userId}?limit=${limit}`);
      return {
        success: true,
        history: response.data.history
      };
    } catch (error) {
      console.error('Chat History API Error:', error.message);
      return {
        success: false,
        history: []
      };
    }
  }
}

module.exports = new ChatbotService();
