'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Plus, 
  Users, 
  BookOpen, 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  MoreVertical,
  Edit,
  Trash2,
  Send,
  Brain,
  Target,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '@/lib/auth-context';
import { assignmentsApi, classesApi } from '@/lib/api-client';
import { AnimatedButton } from '@/components/ui/animated-button';
import { AnimatedCard, AnimatedCardContent, AnimatedCardDescription, AnimatedCardHeader, AnimatedCardTitle } from '@/components/ui/animated-card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-hot-toast';
import { staggerContainer, staggerItem } from '@/lib/animations';
import Link from 'next/link';

interface Assignment {
  id: string;
  title: string;
  description: string;
  dueDate: string;
  priority: 'low' | 'medium' | 'high';
  status: 'draft' | 'published';
  completedCount: number;
  totalStudents: number;
  createdAt: string;
}

interface Student {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  completedAssignments: number;
  totalAssignments: number;
  lastActive: string;
}

interface ClassData {
  id: string;
  name: string;
  description: string;
  subject: string;
  studentCount: number;
  assignmentCount: number;
  createdAt: string;
  color: string;
}

export default function ClassDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isTeacher } = useAuth();
  const [classData, setClassData] = useState<ClassData | null>(null);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateAssignmentOpen, setIsCreateAssignmentOpen] = useState(false);
  const [newAssignment, setNewAssignment] = useState<{
    title: string;
    description: string;
    dueDate: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
  }>({
    title: '',
    description: '',
    dueDate: '',
    priority: 'medium',
  });

  // Fetch real data from API
  const fetchClassData = async () => {
    try {
      const response = await classesApi.getById(params.id as string);
      if (response.success && response.data) {
        setClassData(response.data as any);
      }
    } catch (error) {
      toast.error('Failed to load class data');
    }
  };

  const fetchAssignments = async () => {
    try {
      const response = await assignmentsApi.getAll();
      if (response.success && response.data) {
        // Filter assignments for this class
        const classAssignments = (response.data as any[]).filter(
          (assignment: any) => assignment.classId === params.id as string
        );
        setAssignments(classAssignments);
      }
    } catch (error) {
      toast.error('Failed to load assignments');
    }
  };

  const fetchStudents = async () => {
    try {
      // This would be a specific API call to get class members
      // For now, we'll use an empty array since the API structure isn't clear
      setStudents([]);
    } catch (error) {
      toast.error('Failed to load students');
    }
  };

  useEffect(() => {
    setIsLoading(true);
    Promise.all([
      fetchClassData(),
      fetchAssignments(),
      fetchStudents()
    ]).finally(() => {
      setIsLoading(false);
    });
  }, [params.id]);

  const handleCreateAssignment = async () => {
    if (!newAssignment.title || !newAssignment.dueDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const assignmentData = {
        title: newAssignment.title,
        description: newAssignment.description,
        deadline: new Date(newAssignment.dueDate),
        classId: params.id as string,
      };

      const response = await assignmentsApi.create(assignmentData);

      if (response.success) {
        // Refresh assignments list
        fetchAssignments();
        setNewAssignment({ title: '', description: '', dueDate: '', priority: 'medium' });
        setIsCreateAssignmentOpen(false);
        toast.success('Assignment created successfully!');
      }
    } catch (error) {
      toast.error('Failed to create assignment');
    }
    
    // Simulate AI generating todos for students
    toast.success('Assignment created! AI is generating personalized todos for all students...');
    
    setTimeout(() => {
      toast.success(`✨ AI has created ${classData?.studentCount} personalized todo items for students!`);
    }, 2000);
  };

  const handlePublishAssignment = (assignmentId: string) => {
    setAssignments(prev => 
      prev.map(a => 
        a.id === assignmentId 
          ? { ...a, status: 'published' as const }
          : a
      )
    );
    toast.success('Assignment published! Students will receive notifications.');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getCompletionRate = (completed: number, total: number) => {
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-muted rounded w-64 animate-pulse" />
        <div className="h-32 bg-muted rounded animate-pulse" />
        <div className="grid md:grid-cols-2 gap-6">
          {[1, 2].map(i => (
            <div key={i} className="h-48 bg-muted rounded animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  if (!classData) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold mb-2">Class not found</h2>
        <p className="text-muted-foreground mb-4">The class you&apos;re looking for doesn&apos;t exist.</p>
        <Link href="/dashboard/classes">
          <AnimatedButton>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Classes
          </AnimatedButton>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/classes">
            <AnimatedButton variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </AnimatedButton>
          </Link>
          <div>
            <div className="flex items-center space-x-3">
              <div className={`w-4 h-4 rounded-full ${classData.color}`} />
              <h1 className="text-3xl font-bold">{classData.name}</h1>
            </div>
            <p className="text-muted-foreground">{classData.description}</p>
          </div>
        </div>

        {isTeacher && (
          <Dialog open={isCreateAssignmentOpen} onOpenChange={setIsCreateAssignmentOpen}>
            <DialogTrigger asChild>
              <AnimatedButton animation="scale">
                <Plus className="h-4 w-4 mr-2" />
                Create Assignment
              </AnimatedButton>
            </DialogTrigger>
            <DialogContent size="lg">
              <DialogHeader>
                <DialogTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-blue-500" />
                  <span>AI-Powered Assignment Creator</span>
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Assignment Title *</Label>
                  <Input
                    id="title"
                    value={newAssignment.title}
                    onChange={(e) => setNewAssignment(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="e.g., React Hooks Implementation"
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newAssignment.description}
                    onChange={(e) => setNewAssignment(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe the assignment requirements..."
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="dueDate">Due Date *</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={newAssignment.dueDate}
                      onChange={(e) => setNewAssignment(prev => ({ ...prev, dueDate: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <select
                      id="priority"
                      value={newAssignment.priority}
                      onChange={(e) => setNewAssignment(prev => ({ ...prev, priority: e.target.value as 'low' | 'medium' | 'high' | 'urgent' }))}
                      className="w-full px-3 py-2 border border-input rounded-md"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Brain className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium text-blue-700 dark:text-blue-300">AI Enhancement</span>
                  </div>
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    Our AI will automatically create personalized todo items for each student based on their learning progress and the assignment requirements.
                  </p>
                </div>
                <div className="flex justify-end space-x-2">
                  <AnimatedButton 
                    variant="outline" 
                    onClick={() => setIsCreateAssignmentOpen(false)}
                  >
                    Cancel
                  </AnimatedButton>
                  <AnimatedButton onClick={handleCreateAssignment}>
                    <Brain className="h-4 w-4 mr-2" />
                    Create with AI
                  </AnimatedButton>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Stats Cards */}
      <motion.div
        className="grid md:grid-cols-4 gap-6"
        variants={staggerContainer}
        initial="initial"
        animate="animate"
      >
        {[
          { 
            title: 'Total Students', 
            value: classData.studentCount, 
            icon: Users, 
            color: 'text-blue-500' 
          },
          { 
            title: 'Assignments', 
            value: assignments.length, 
            icon: BookOpen, 
            color: 'text-green-500' 
          },
          { 
            title: 'Avg Completion', 
            value: `${Math.round(assignments.reduce((acc, a) => acc + getCompletionRate(a.completedCount, a.totalStudents), 0) / assignments.length || 0)}%`, 
            icon: TrendingUp, 
            color: 'text-purple-500' 
          },
          { 
            title: 'Active Students', 
            value: students.filter(s => new Date(s.lastActive) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length, 
            icon: Target, 
            color: 'text-orange-500' 
          },
        ].map((stat, index) => (
          <motion.div key={index} variants={staggerItem}>
            <AnimatedCard>
              <AnimatedCardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">{stat.title}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </motion.div>
        ))}
      </motion.div>

      {/* Main Content */}
      <Tabs defaultValue="assignments" className="space-y-6">
        <TabsList>
          <TabsTrigger value="assignments">Assignments</TabsTrigger>
          <TabsTrigger value="students">Students</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="assignments" className="space-y-6">
          <motion.div
            className="space-y-4"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {assignments.map((assignment, index) => (
              <motion.div key={assignment.id} variants={staggerItem}>
                <AnimatedCard className="hover:shadow-md transition-shadow">
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <div className={`w-3 h-3 rounded-full ${getPriorityColor(assignment.priority)}`} />
                          <h3 className="text-lg font-semibold">{assignment.title}</h3>
                          <Badge variant={assignment.status === 'published' ? 'default' : 'secondary'}>
                            {assignment.status}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground mb-3">{assignment.description}</p>
                        <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>Due: {new Date(assignment.dueDate).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <CheckCircle className="h-4 w-4" />
                            <span>{assignment.completedCount}/{assignment.totalStudents} completed</span>
                          </div>
                        </div>
                        <div className="mt-3">
                          <div className="flex items-center justify-between text-sm mb-1">
                            <span>Completion Rate</span>
                            <span>{getCompletionRate(assignment.completedCount, assignment.totalStudents)}%</span>
                          </div>
                          <Progress 
                            value={getCompletionRate(assignment.completedCount, assignment.totalStudents)} 
                            className="h-2"
                          />
                        </div>
                      </div>
                      
                      {isTeacher && (
                        <div className="flex items-center space-x-2">
                          {assignment.status === 'draft' && (
                            <AnimatedButton
                              size="sm"
                              onClick={() => handlePublishAssignment(assignment.id)}
                            >
                              <Send className="h-4 w-4 mr-1" />
                              Publish
                            </AnimatedButton>
                          )}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <AnimatedButton variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </AnimatedButton>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )}
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </motion.div>
            ))}
          </motion.div>
        </TabsContent>

        <TabsContent value="students" className="space-y-6">
          <motion.div
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {students.map((student, index) => (
              <motion.div key={student.id} variants={staggerItem}>
                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <Avatar>
                        <AvatarImage src={student.avatar} />
                        <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{student.name}</h3>
                        <p className="text-sm text-muted-foreground">{student.email}</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{student.completedAssignments}/{student.totalAssignments}</span>
                      </div>
                      <Progress 
                        value={getCompletionRate(student.completedAssignments, student.totalAssignments)} 
                        className="h-2"
                      />
                      <p className="text-xs text-muted-foreground">
                        Last active: {new Date(student.lastActive).toLocaleDateString()}
                      </p>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </motion.div>
            ))}
          </motion.div>
        </TabsContent>

        <TabsContent value="analytics">
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Class Analytics</AnimatedCardTitle>
              <AnimatedCardDescription>
                Detailed insights about class performance and engagement
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-center py-12">
                <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Analytics Coming Soon</h3>
                <p className="text-muted-foreground">
                  Advanced analytics and insights will be available in the next update.
                </p>
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>
      </Tabs>
    </div>
  );
}
