'use client';

import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { TodoManager } from '@/components/features/todo-manager';
import { AnimatedButton } from '@/components/ui/animated-button';
import { fadeInUp } from '@/lib/animations';

export default function TodosPage() {
  return (
    <motion.div
      className="p-6 space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <motion.div
        className="flex items-center justify-between mb-8"
        {...fadeInUp}
      >
        <div className="flex items-center space-x-4">
          <AnimatedButton
            variant="ghost"
            size="sm"
            asChild
            className="p-2"
          >
            <Link href="/dashboard">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </AnimatedButton>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Todo Manager</h1>
            <p className="text-gray-600 mt-1">Manage your tasks and assignments</p>
          </div>
        </div>
      </motion.div>

      {/* Todo Manager Component */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        <TodoManager />
      </motion.div>
    </motion.div>
  );
}
