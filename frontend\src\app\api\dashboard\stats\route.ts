import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Role } from '@prisma/client';

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    const userRole = session.user.role;

    let stats: Record<string, unknown> = {};

    if (userRole === Role.STUDENT) {
      // Student dashboard stats
      const [
        enrolledClasses,
        totalTodos,
        completedTodos,
        overdueTodos,
        upcomingDeadlines,
        aiLearningPaths,
        recentActivity
      ] = await Promise.all([
        // Enrolled classes count
        prisma.classMember.count({
          where: { userId }
        }),

        // Total todos
        prisma.todo.count({
          where: { userId }
        }),

        // Completed todos
        prisma.todo.count({
          where: {
            userId,
            status: 'COMPLETED'
          }
        }),

        // Overdue todos
        prisma.todo.count({
          where: {
            userId,
            status: 'OVERDUE'
          }
        }),

        // Upcoming deadlines (next 7 days)
        prisma.todo.count({
          where: {
            userId,
            deadline: {
              gte: new Date(),
              lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
            },
            status: {
              in: ['PENDING', 'IN_PROGRESS']
            }
          }
        }),

        // AI learning paths
        prisma.aiLearningPath.count({
          where: {
            userId,
            isActive: true
          }
        }),

        // Recent activity (last 10 items)
        prisma.todo.findMany({
          where: { userId },
          include: {
            assignment: {
              include: {
                class: {
                  select: { name: true }
                }
              }
            },
            aiPath: {
              select: { title: true }
            }
          },
          orderBy: { updatedAt: 'desc' },
          take: 10
        })
      ]);

      // Calculate study streak (consecutive days with completed todos)
      const studyStreak = await calculateStudyStreak(userId);

      stats = {
        totalClasses: enrolledClasses,
        totalTodos,
        completedTodos,
        overdueTodos,
        upcomingDeadlines,
        aiLearningPaths,
        studyStreak,
        completionRate: totalTodos > 0 ? Math.round((completedTodos / totalTodos) * 100) : 0,
        recentActivity: recentActivity.map((todo: any) => ({
          id: todo.id,
          type: todo.source === 'TEACHER_ASSIGNMENT' ? 'assignment' : 
                todo.source === 'AI_LEARNING_PATH' ? 'ai_path' : 'personal',
          title: todo.title,
          status: todo.status,
          updatedAt: todo.updatedAt,
          context: todo.assignment?.class?.name || todo.aiPath?.title || 'Personal'
        }))
      };

    } else if (userRole === Role.TEACHER || userRole === Role.ADMIN) {
      // Teacher dashboard stats
      const [
        teachingClasses,
        totalAssignments,
        totalStudents,
        pendingSubmissions,
        recentAssignments
      ] = await Promise.all([
        // Teaching classes count
        prisma.class.count({
          where: { teacherId: userId }
        }),

        // Total assignments created
        prisma.assignment.count({
          where: { teacherId: userId }
        }),

        // Total students across all classes
        prisma.classMember.count({
          where: {
            class: {
              teacherId: userId
            }
          }
        }),

        // Pending submissions (todos from assignments not completed)
        prisma.todo.count({
          where: {
            assignment: {
              teacherId: userId
            },
            status: {
              in: ['PENDING', 'IN_PROGRESS']
            }
          }
        }),

        // Recent assignments
        prisma.assignment.findMany({
          where: { teacherId: userId },
          include: {
            class: {
              select: { name: true }
            },
            _count: {
              select: { todos: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        })
      ]);

      stats = {
        totalClasses: teachingClasses,
        totalAssignments,
        totalStudents,
        pendingSubmissions,
        recentAssignments: recentAssignments.map((assignment: any) => ({
          id: assignment.id,
          title: assignment.title,
          className: assignment.class.name,
          deadline: assignment.deadline,
          totalTodos: assignment._count.todos,
          createdAt: assignment.createdAt
        }))
      };
    }

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function calculateStudyStreak(userId: string): Promise<number> {
  try {
    // Get completed todos grouped by date
    const completedTodos = await prisma.todo.findMany({
      where: {
        userId,
        status: 'COMPLETED',
        completedAt: {
          not: null
        }
      },
      select: {
        completedAt: true
      },
      orderBy: {
        completedAt: 'desc'
      }
    });

    if (completedTodos.length === 0) return 0;

    // Group by date
    const dateSet = new Set();
    completedTodos.forEach((todo: any) => {
      if (todo.completedAt) {
        const date = todo.completedAt.toISOString().split('T')[0];
        dateSet.add(date);
      }
    });

    const uniqueDates = Array.from(dateSet).sort().reverse();
    
    // Calculate consecutive days from today
    let streak = 0;
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Check if there's activity today or yesterday
    if (uniqueDates.includes(today)) {
      streak = 1;
      const currentDate = new Date();
      
      for (let i = 1; i < uniqueDates.length; i++) {
        currentDate.setDate(currentDate.getDate() - 1);
        const expectedDate = currentDate.toISOString().split('T')[0];
        
        if (uniqueDates.includes(expectedDate)) {
          streak++;
        } else {
          break;
        }
      }
    } else if (uniqueDates.includes(yesterday)) {
      // If no activity today but had yesterday, streak is broken but we can show yesterday's streak
      streak = 1;
      const currentDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      for (let i = 1; i < uniqueDates.length; i++) {
        currentDate.setDate(currentDate.getDate() - 1);
        const expectedDate = currentDate.toISOString().split('T')[0];
        
        if (uniqueDates.includes(expectedDate)) {
          streak++;
        } else {
          break;
        }
      }
    }

    return streak;
  } catch (error) {
    console.error('Error calculating study streak:', error);
    return 0;
  }
}
