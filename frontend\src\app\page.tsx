'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { AnimatedButton } from '@/components/ui/animated-button';
import { Animated<PERSON>ard, AnimatedCardContent, AnimatedCardDescription, AnimatedCardHeader, AnimatedCardTitle, GlowCard, FloatingCard } from '@/components/ui/animated-card';
import { FloatingParticles, StarField } from '@/components/effects/particle-system';
import { AuroraBackground, NeuralBackground } from '@/components/effects/background-effects';
import {
  BookOpen,
  Brain,
  Users,
  Calendar,
  CheckSquare,
  Zap,
  ArrowRight,
  Star,
  Trophy,
  Target,
  Sparkles,
  Rocket,
  Shield,
  Globe,
  Cpu,
  Database,
  Code,
  Layers,
  Wifi,
  Lock
} from 'lucide-react';
import {
  fadeInUp,
  fadeInDown,
  fadeInLeft,
  fadeInRight,
  staggerContainer,
  staggerItem,
  floatingAnimation,
  pulseAnimation,
  gradientShift,
  magneticHover,
  morphingBlob,
  neonGlow,
  holographicShimmer,
  cyberpunkGlitch,
  quantumTunnel
} from '@/lib/animations';

export default function HomePage() {

  const features = [
    {
      icon: <Brain className="h-8 w-8" />,
      title: "AI-Powered Learning",
      description: "Personalized learning paths generated by advanced AI to match your goals and pace.",
      color: "from-blue-500 to-purple-600",
      delay: 0
    },
    {
      icon: <CheckSquare className="h-8 w-8" />,
      title: "Smart Todo Management",
      description: "Automatic todo generation from teacher assignments and AI learning recommendations.",
      color: "from-green-500 to-blue-600",
      delay: 0.1
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "Collaborative Learning",
      description: "Join study groups, work on assignments together, and learn from your peers.",
      color: "from-purple-500 to-pink-600",
      delay: 0.2
    },
    {
      icon: <Calendar className="h-8 w-8" />,
      title: "Deadline Management",
      description: "Never miss a deadline with smart notifications and calendar integration.",
      color: "from-orange-500 to-red-600",
      delay: 0.3
    },
    {
      icon: <BookOpen className="h-8 w-8" />,
      title: "Class Management",
      description: "Teachers can create classes, assign tasks, and track student progress effortlessly.",
      color: "from-yellow-500 to-orange-600",
      delay: 0.4
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Real-time Updates",
      description: "Stay connected with instant notifications and real-time collaboration features.",
      color: "from-cyan-500 to-blue-600",
      delay: 0.5
    }
  ];

  const techFeatures = [
    {
      icon: <Cpu className="h-6 w-6" />,
      title: "Next.js 15",
      description: "Latest React framework",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Database className="h-6 w-6" />,
      title: "Real-time DB",
      description: "Live collaboration",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: <Code className="h-6 w-6" />,
      title: "TypeScript",
      description: "Type-safe development",
      color: "from-purple-500 to-violet-500"
    },
    {
      icon: <Layers className="h-6 w-6" />,
      title: "Microservices",
      description: "Scalable architecture",
      color: "from-orange-500 to-red-500"
    },
    {
      icon: <Wifi className="h-6 w-6" />,
      title: "Socket.io",
      description: "Real-time features",
      color: "from-pink-500 to-rose-500"
    },
    {
      icon: <Lock className="h-6 w-6" />,
      title: "Secure Auth",
      description: "Enterprise security",
      color: "from-indigo-500 to-blue-500"
    },
    {
      icon: <Sparkles className="h-6 w-6" />,
      title: "AI-Powered",
      description: "Smart learning assistant",
      color: "from-yellow-500 to-orange-500"
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: "Enterprise Grade",
      description: "Production ready",
      color: "from-gray-500 to-slate-500"
    }
  ];

  const stats = [
    { icon: <Users className="h-6 w-6" />, label: "Active Students", value: "10,000+" },
    { icon: <BookOpen className="h-6 w-6" />, label: "Courses Available", value: "500+" },
    { icon: <Trophy className="h-6 w-6" />, label: "Success Rate", value: "95%" },
    { icon: <Target className="h-6 w-6" />, label: "Goals Achieved", value: "50,000+" }
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Lightweight Background Effects */}
      <AuroraBackground intensity="medium" />
      <NeuralBackground intensity="low" />
      <StarField particleCount={30} />
      <FloatingParticles particleCount={15} colors={['#3b82f6', '#8b5cf6', '#06b6d4']} />

      <Header />

      {/* Spectacular Animated Background Elements */}
      <motion.div
        className="absolute top-20 left-10 w-72 h-72 rounded-full blur-3xl"
        style={{
          background: 'linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(139, 92, 246, 0.3))'
        }}
        animate={{
          borderRadius: [
            "60% 40% 30% 70%/60% 30% 70% 40%",
            "30% 60% 70% 40%/50% 60% 30% 60%",
            "40% 60% 60% 40%/60% 40% 60% 40%",
            "60% 40% 30% 70%/60% 30% 70% 40%"
          ],
          scale: [1, 1.1, 0.9, 1],
          rotate: [0, 90, 180, 360]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <motion.div
        className="absolute bottom-20 right-10 w-96 h-96 rounded-full blur-3xl"
        style={{
          background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(236, 72, 153, 0.3))'
        }}
        animate={{
          scale: [1, 1.5, 1],
          opacity: [1, 0.3, 1],
          filter: [
            "blur(20px) hue-rotate(0deg)",
            "blur(40px) hue-rotate(180deg)",
            "blur(20px) hue-rotate(360deg)"
          ]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Floating Tech Icons */}
      {techFeatures.map((tech, index) => (
        <motion.div
          key={tech.title}
          className="absolute hidden lg:block"
          style={{
            left: `${10 + (index * 15)}%`,
            top: `${20 + (index % 3) * 20}%`
          }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: [0.3, 0.7],
            scale: [0.8, 1.2],
            rotate: [0, 360]
          }}
          transition={{
            duration: 8 + index,
            repeat: Infinity,
            delay: index * 0.5
          }}
        >
          <div className="p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
            {tech.icon}
          </div>
        </motion.div>
      ))}

      {/* Main Content */}
      <div className="relative z-10">

      {/* Hero Section */}
      <section className="py-20 px-4 relative">
        {/* Aurora Background Effect */}
        <AuroraBackground />

        <motion.div
          className="container mx-auto text-center relative z-10"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          <div className="max-w-4xl mx-auto">
            <motion.div
              variants={staggerItem}
              className="mb-6"
            >
              <motion.h1
                className="text-5xl md:text-7xl font-bold mb-6 leading-tight relative"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              >
                <motion.span
                  className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent"
                  style={{ backgroundSize: '200% 200%' }}
                  animate={{
                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                >
                  AI-Powered Learning Platform
                </motion.span>

                <motion.span
                  className="block text-4xl md:text-5xl mt-2 relative"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3, duration: 0.6, type: "spring" }}
                >
                  <motion.span
                    className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"
                    animate={{
                      textShadow: [
                        "0 0 10px rgba(139, 92, 246, 0.5)",
                        "0 0 20px rgba(139, 92, 246, 0.8)",
                        "0 0 10px rgba(139, 92, 246, 0.5)"
                      ]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    for FPT University
                  </motion.span>

                  {/* Floating sparkles */}
                  {Array.from({ length: 6 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                      style={{
                        left: `${20 + i * 15}%`,
                        top: `${10 + (i % 2) * 20}%`
                      }}
                      animate={{
                        scale: [0, 1],
                        opacity: [0, 1],
                        rotate: [0, 360]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: i * 0.3,
                        ease: "easeInOut"
                      }}
                    />
                  ))}
                </motion.span>
              </motion.h1>
            </motion.div>

            <motion.p
              className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed"
              variants={staggerItem}
            >
              Transform your learning experience with{' '}
              <motion.span
                className="text-blue-600 font-semibold"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                intelligent todo management
              </motion.span>
              ,{' '}
              <motion.span
                className="text-purple-600 font-semibold"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                personalized AI tutoring
              </motion.span>
              , and{' '}
              <motion.span
                className="text-pink-600 font-semibold"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                seamless collaboration tools
              </motion.span>
              .
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
              variants={fadeInUp}
              initial="initial"
              animate="animate"
              transition={{ delay: 0.8 }}
            >
              <motion.div
                variants={fadeInLeft}
                initial="initial"
                animate="animate"
                transition={{ delay: 1.0 }}
              >
                <Link href="/auth/signup">
                  <AnimatedButton
                    size="xl"
                    variant="gradient"
                    icon={<Rocket className="h-5 w-5" />}
                    rightIcon={<ArrowRight className="h-5 w-5" />}
                    className="text-lg px-10 py-4"
                  >
                    Start Learning Today
                  </AnimatedButton>
                </Link>
              </motion.div>
              <motion.div
                variants={fadeInRight}
                initial="initial"
                animate="animate"
                transition={{ delay: 1.2 }}
              >
                <Link href="/demo">
                  <AnimatedButton
                    variant="glass"
                    size="xl"
                    icon={<Globe className="h-5 w-5" />}
                    className="text-lg px-10 py-4"
                  >
                    Watch Demo
                  </AnimatedButton>
                </Link>
              </motion.div>
            </motion.div>

            {/* Floating Icons */}
            <motion.div className="relative">
              <motion.div
                className="absolute -top-10 -left-10 text-blue-500"
                animate={{
                  y: [-10, 10, -10],
                  rotate: [0, 10, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Brain className="h-8 w-8" />
              </motion.div>
              <motion.div
                className="absolute -top-5 -right-5 text-purple-500"
                animate={{
                  y: [10, -10, 10],
                  rotate: [0, -10, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Zap className="h-6 w-6" />
              </motion.div>
              <motion.div
                className="absolute top-5 left-5 text-pink-500"
                animate={{
                  scale: [1, 1.2, 1],
                  rotate: [0, 180, 360],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Star className="h-5 w-5" />
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white/50 backdrop-blur-sm">
        <motion.div
          className="container mx-auto px-4"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                variants={staggerItem}
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div
                  className="flex justify-center mb-4"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="p-4 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full text-blue-600 shadow-lg">
                    {stat.icon}
                  </div>
                </motion.div>
                <motion.div
                  className="text-4xl font-bold text-gray-900 mb-2"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.2 + index * 0.1, type: "spring", stiffness: 200 }}
                >
                  {stat.value}
                </motion.div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 relative">
        {/* Floating Particles Effect */}
        <FloatingParticles />

        <motion.div className="container mx-auto relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <motion.h2
              className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
              variants={fadeInDown}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Powerful Features for Modern Learning
            </motion.h2>
            <motion.p
              className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              Everything you need to succeed in your academic journey, powered by cutting-edge AI technology.
            </motion.p>
          </motion.div>

          <motion.div
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={staggerItem}
                whileHover={{ y: -10 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div
                  className="h-full group cursor-pointer relative overflow-hidden"
                  style={{
                    background: `linear-gradient(135deg, ${feature.color.split(' ')[1]}, ${feature.color.split(' ')[3]})`
                  }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ delay: feature.delay, duration: 0.6 }}
                  whileHover={{
                    scale: 1.05,
                    boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
                  }}
                >
                  {/* Animated background gradient */}
                  <motion.div
                    className="absolute inset-0 opacity-10"
                    animate={{
                      background: [
                        `linear-gradient(45deg, ${feature.color.split(' ')[1]}, ${feature.color.split(' ')[3]})`,
                        `linear-gradient(135deg, ${feature.color.split(' ')[3]}, ${feature.color.split(' ')[1]})`,
                        `linear-gradient(225deg, ${feature.color.split(' ')[1]}, ${feature.color.split(' ')[3]})`,
                        `linear-gradient(315deg, ${feature.color.split(' ')[3]}, ${feature.color.split(' ')[1]})`
                      ]
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />

                  <div className="relative z-10 p-6 bg-white/90 backdrop-blur-sm rounded-xl border border-white/20 h-full">
                    <AnimatedCardHeader>
                      <motion.div
                        className="w-14 h-14 rounded-xl flex items-center justify-center text-white mb-4 shadow-lg relative"
                        style={{
                          background: `linear-gradient(135deg, ${feature.color.split(' ')[1]}, ${feature.color.split(' ')[3]})`
                        }}
                        whileHover={{
                          scale: 1.2,
                          rotate: 360
                        }}
                        animate={{
                          boxShadow: [
                            "0 0 20px rgba(59, 130, 246, 0.3)",
                            "0 0 30px rgba(139, 92, 246, 0.5)",
                            "0 0 20px rgba(59, 130, 246, 0.3)"
                          ]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        {feature.icon}

                        {/* Floating particles around icon */}
                        {Array.from({ length: 3 }).map((_, i) => (
                          <motion.div
                            key={i}
                            className="absolute w-1 h-1 bg-white rounded-full"
                            style={{
                              left: `${20 + i * 20}%`,
                              top: `${20 + i * 20}%`
                            }}
                            animate={{
                              scale: [0, 1],
                              opacity: [0, 1],
                              x: [0, 10],
                              y: [0, -10]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              delay: i * 0.5
                            }}
                          />
                        ))}
                      </motion.div>

                      <motion.h3
                        className="text-xl font-semibold mb-3 text-gray-800"
                        variants={cyberpunkGlitch}
                        initial="initial"
                        whileInView="animate"
                        viewport={{ once: true }}
                        transition={{ delay: feature.delay + 0.2 }}
                      >
                        {feature.title}
                      </motion.h3>
                    </AnimatedCardHeader>

                    <AnimatedCardContent>
                      <motion.p
                        className="text-gray-600 leading-relaxed"
                        variants={gradientShift}
                        initial="initial"
                        whileInView="animate"
                        viewport={{ once: true }}
                        transition={{ delay: feature.delay + 0.4 }}
                      >
                        {feature.description}
                      </motion.p>
                    </AnimatedCardContent>

                    {/* Hover Effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    />
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900 relative">
        {/* Star Field Effect */}
        <StarField />

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              What Students Say
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Hear from FPT University students who have transformed their learning experience with UniHub.
            </p>
          </motion.div>

          <motion.div
            className="grid md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {[
              {
                name: "Nguyen Van A",
                role: "Computer Science Student",
                content: "FPT UniHub's AI tutor helped me understand complex algorithms in ways I never thought possible. My grades improved by 40%!",
                avatar: "👨‍💻"
              },
              {
                name: "Tran Thi B",
                role: "Business Administration Student",
                content: "The collaborative features and smart todo management keep me organized and connected with my study group.",
                avatar: "👩‍💼"
              },
              {
                name: "Le Van C",
                role: "Engineering Student",
                content: "The AI-powered learning paths are incredible. It's like having a personal tutor available 24/7.",
                avatar: "👨‍🔬"
              }
            ].map((testimonial, index) => (
              <motion.div key={index} variants={staggerItem}>
                <GlowCard className="h-full text-center p-8 hover:shadow-xl transition-shadow">
                  <AnimatedCardContent>
                    <div className="text-4xl mb-4">{testimonial.avatar}</div>
                    <AnimatedCardDescription className="text-muted-foreground mb-6 italic">
                      &quot;{testimonial.content}&quot;
                    </AnimatedCardDescription>
                    <div>
                      <AnimatedCardTitle className="font-semibold text-lg">
                        {testimonial.name}
                      </AnimatedCardTitle>
                      <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                    </div>
                  </AnimatedCardContent>
                </GlowCard>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            className="grid md:grid-cols-4 gap-8 text-center"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {stats.map((stat, index) => (
              <motion.div key={index} variants={staggerItem}>
                <FloatingCard className="p-6 h-full">
                  <motion.div
                    className="text-blue-600 mb-4 flex justify-center"
                    variants={pulseAnimation}
                    animate="animate"
                    whileHover={magneticHover}
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                  >
                    {stat.icon}
                  </motion.div>
                  <motion.div
                    className="text-4xl font-bold text-foreground mb-2"
                    variants={floatingAnimation}
                    initial="initial"
                    animate="animate"
                    whileInView={{ opacity: 1, scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                  >
                    {stat.value}
                  </motion.div>
                  <p className="text-muted-foreground">{stat.label}</p>
                </FloatingCard>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white relative overflow-hidden">
        {/* Neural Background Effect */}
        <NeuralBackground />

        {/* Background Animation */}
        <motion.div
          className="absolute inset-0 opacity-10"
          animate={{
            backgroundPosition: ['0% 0%', '100% 100%'],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: 'reverse',
          }}
          style={{
            backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',
            backgroundSize: '50px 50px',
          }}
        />

        <div className="container mx-auto px-4 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Transform Your Learning?</h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Join thousands of FPT University students who are already using AI to accelerate their learning journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/signup">
                <AnimatedButton
                  size="lg"
                  variant="secondary"
                  className="text-lg px-8 py-4 bg-white text-blue-600 hover:bg-gray-100"
                  animation="bounce"
                >
                  Get Started for Free
                  <Star className="ml-2 h-5 w-5" />
                </AnimatedButton>
              </Link>
              <Link href="/demo">
                <AnimatedButton
                  size="lg"
                  variant="outline"
                  className="text-lg px-8 py-4 border-white text-white hover:bg-white/10"
                  animation="pulse"
                >
                  Watch Demo
                  <Rocket className="ml-2 h-5 w-5" />
                </AnimatedButton>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
      </div>
    </div>
  );
}
