'use client';

import { io, Socket } from 'socket.io-client';
import { toast } from 'react-hot-toast';

interface ServerToClientEvents {
  notification: (data: any) => void;
  todoUpdate: (data: any) => void;
  classUpdate: (data: any) => void;
  assignmentUpdate: (data: any) => void;
  userOnline: (data: { userId: string; status: 'online' | 'offline' }) => void;
  dashboardUpdate: (data: any) => void;
  aiResponse: (data: any) => void;
}

interface ClientToServerEvents {
  joinRoom: (roomId: string) => void;
  leaveRoom: (roomId: string) => void;
  sendMessage: (data: any) => void;
  updateStatus: (status: 'online' | 'offline') => void;
}

class WebSocketClient {
  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, ((...args: unknown[]) => void)[]> = new Map();

  connect(userId?: string) {
    if (this.socket?.connected) return;

    const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:5000';
    
    this.socket = io(socketUrl, {
      auth: {
        userId,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      this.reconnectAttempts = 0;
      toast.success('Connected to real-time updates', {
        duration: 2000,
        position: 'bottom-right',
      });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.handleReconnect();
    });

    // Real-time event handlers
    this.socket.on('notification', (data) => {
      this.emit('notification', data);
      toast.success(data.title, {
        duration: 4000,
      });
    });

    this.socket.on('todoUpdate', (data) => {
      this.emit('todoUpdate', data);
    });

    this.socket.on('classUpdate', (data) => {
      this.emit('classUpdate', data);
    });

    this.socket.on('assignmentUpdate', (data) => {
      this.emit('assignmentUpdate', data);
    });

    this.socket.on('dashboardUpdate', (data) => {
      this.emit('dashboardUpdate', data);
    });

    this.socket.on('userOnline', (data) => {
      this.emit('userOnline', data);
    });

    this.socket.on('aiResponse', (data) => {
      this.emit('aiResponse', data);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      toast.error('Failed to connect to real-time updates', {
        duration: 5000,
      });
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      console.log(`🔄 Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.socket?.connect();
    }, delay);
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.listeners.clear();
  }

  // Event emitter methods
  on(event: string, callback: (...args: unknown[]) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: (...args: unknown[]) => void) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data));
    }
  }

  // Room management
  joinRoom(roomId: string) {
    this.socket?.emit('joinRoom', roomId);
  }

  leaveRoom(roomId: string) {
    this.socket?.emit('leaveRoom', roomId);
  }

  // Send messages
  sendMessage(data: any) {
    this.socket?.emit('sendMessage', data);
  }

  // Update user status
  updateStatus(status: 'online' | 'offline') {
    this.socket?.emit('updateStatus', status);
  }

  // Check connection status
  get isConnected() {
    return this.socket?.connected || false;
  }

  // Get socket instance
  get socketInstance() {
    return this.socket;
  }
}

// Create singleton instance
export const wsClient = new WebSocketClient();

// React hook for WebSocket
export function useWebSocket() {
  return {
    connect: wsClient.connect.bind(wsClient),
    disconnect: wsClient.disconnect.bind(wsClient),
    on: wsClient.on.bind(wsClient),
    off: wsClient.off.bind(wsClient),
    joinRoom: wsClient.joinRoom.bind(wsClient),
    leaveRoom: wsClient.leaveRoom.bind(wsClient),
    sendMessage: wsClient.sendMessage.bind(wsClient),
    updateStatus: wsClient.updateStatus.bind(wsClient),
    isConnected: wsClient.isConnected,
  };
}

export default wsClient;
