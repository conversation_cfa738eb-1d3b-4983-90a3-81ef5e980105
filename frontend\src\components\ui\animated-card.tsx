'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { cardHover, fadeInUp } from '@/lib/animations';

interface AnimatedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'gradient' | 'neon' | 'elevated';
  hover?: boolean;
  delay?: number;
  children: React.ReactNode;
}

const cardVariants = {
  default: 'bg-card text-card-foreground border border-border shadow-sm',
  glass: 'bg-white/10 backdrop-blur-md border border-white/20 text-white shadow-lg',
  gradient: 'bg-gradient-to-br from-blue-50 via-white to-purple-50 border border-blue-200/50 shadow-lg',
  neon: 'bg-gray-900 border border-cyan-400/50 shadow-lg shadow-cyan-400/10',
  elevated: 'bg-white shadow-xl border-0',
};

const AnimatedCard = React.forwardRef<HTMLDivElement, AnimatedCardProps>(
  ({ className, variant = 'default', hover = true, delay = 0, children, ...props }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn(
          'rounded-xl overflow-hidden transition-all duration-300',
          cardVariants[variant],
          className
        )}
        initial="initial"
        animate="animate"
        variants={fadeInUp}
        transition={{ ...fadeInUp.transition, delay }}
        {...(hover ? cardHover : {})}
        {...(props as any)}
      >
        {children}
      </motion.div>
    );
  }
);

AnimatedCard.displayName = 'AnimatedCard';

const AnimatedCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  // Filter out conflicting props
  const { onAnimationStart, onAnimationEnd, onDrag, onDragEnd, onDragStart, ...motionProps } = props as any;

  return (
    <motion.div
      ref={ref}
      className={cn('flex flex-col space-y-1.5 p-6', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1, duration: 0.5 }}
      {...motionProps}
    />
  );
});
AnimatedCardHeader.displayName = 'AnimatedCardHeader';

const AnimatedCardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => {
  const { onAnimationStart, onAnimationEnd, onDrag, onDragEnd, onDragStart, ...motionProps } = props as any;

  return (
    <motion.h3
      ref={ref}
      className={cn('font-semibold leading-none tracking-tight', className)}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.2, duration: 0.5 }}
      {...motionProps}
    />
  );
});
AnimatedCardTitle.displayName = 'AnimatedCardTitle';

const AnimatedCardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { onAnimationStart, onAnimationEnd, onDrag, onDragEnd, onDragStart, ...motionProps } = props as any;

  return (
    <motion.p
      ref={ref}
      className={cn('text-sm text-muted-foreground', className)}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.3, duration: 0.5 }}
      {...motionProps}
    />
  );
});
AnimatedCardDescription.displayName = 'AnimatedCardDescription';

const AnimatedCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { onAnimationStart, onAnimationEnd, onDrag, onDragEnd, onDragStart, ...motionProps } = props as any;

  return (
    <motion.div
      ref={ref}
      className={cn('p-6 pt-0', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.4, duration: 0.5 }}
      {...motionProps}
    />
  );
});
AnimatedCardContent.displayName = 'AnimatedCardContent';

const AnimatedCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { onAnimationStart, onAnimationEnd, onDrag, onDragEnd, onDragStart, ...motionProps } = props as any;

  return (
    <motion.div
      ref={ref}
      className={cn('flex items-center p-6 pt-0', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.5, duration: 0.5 }}
      {...motionProps}
    />
  );
});
AnimatedCardFooter.displayName = 'AnimatedCardFooter';

// Special Card Components
const GlowCard = React.forwardRef<HTMLDivElement, AnimatedCardProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn(
          'relative rounded-xl p-6 bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200/50 overflow-hidden',
          className
        )}
        whileHover={{
          boxShadow: '0 0 30px rgba(59, 130, 246, 0.3)',
          scale: 1.02,
        }}
        transition={{ duration: 0.3 }}
        {...(props as any)}
      >
        {/* Animated Background */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-pink-400/10"
          animate={{
            backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          style={{
            backgroundSize: '200% 200%',
          }}
        />
        
        {/* Content */}
        <div className="relative z-10">
          {children}
        </div>
      </motion.div>
    );
  }
);

GlowCard.displayName = 'GlowCard';

const FloatingCard = React.forwardRef<HTMLDivElement, AnimatedCardProps>(
  ({ className, children, delay = 0, ...props }, ref) => {
    const { onAnimationStart, onAnimationEnd, onDrag, onDragEnd, onDragStart, ...motionProps } = props as any;

    return (
      <motion.div
        ref={ref}
        className={cn(
          'rounded-xl p-6 bg-white shadow-lg border border-gray-200/50',
          className
        )}
        initial={{ opacity: 0, y: 50 }}
        animate={{
          opacity: 1,
          y: 0,
        }}
        transition={{
          delay,
          duration: 0.6
        }}
        whileHover={{
          y: -10,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        }}
        {...motionProps}
      >
        <motion.div
          animate={{
            y: [-2, 2, -2],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          {children}
        </motion.div>
      </motion.div>
    );
  }
);

FloatingCard.displayName = 'FloatingCard';

export {
  AnimatedCard,
  AnimatedCardHeader,
  AnimatedCardFooter,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardContent,
  GlowCard,
  FloatingCard,
};
