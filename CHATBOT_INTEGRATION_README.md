# 🤖 FPT UniHub Chatbot Integration

## 📋 Tổng quan

Hệ thống chatbot đã được tích hợp hoàn chỉnh với web application FPT UniHub, bao gồm:

### 🎯 Các Agent được tích hợp:

1. **🏫 RAG Agent (School Info)**
   - Trả lời câu hỏi về thông tin trường học
   - Tích hợp với database để lấy thông tin thời gian thực
   - Hỗ trợ: học ph<PERSON>, tuy<PERSON><PERSON> sinh, nộ<PERSON> quy, mô<PERSON> học, lớp học

2. **📋 Schedule Agent (Task Manager)**
   - Quản lý todo list
   - Tích hợp với database Todos của web app
   - Hỗ trợ: tạo, xem, sửa, xóa task

3. **🤖 Generic Agent (General AI)**
   - Tr<PERSON> lời câu hỏi chung
   - Tìm kiếm thông tin trên internet
   - Tr<PERSON> chuyện thường ngày

### ✨ Tính năng đã hoàn thiện:

- ✅ Multi-agent routing thông minh
- ✅ Tích hợp database PostgreSQL
- ✅ Chat history và session management
- ✅ UI/UX cải tiến với agent type indicators
- ✅ Typing indicators và suggestions
- ✅ Health monitoring và error handling

## 🚀 Cách khởi động hệ thống

### ⭐ Phương pháp mới: Auto-Integrated (Khuyến nghị)

```bash
# Chỉ cần khởi động backend, chatbot sẽ tự động start!
cd backend
npm start
```

Sau đó mở terminal khác:
```bash
cd frontend
npm run dev
```

**Chatbot sẽ tự động khởi động cùng với backend!** 🎉

### Phương pháp 1: Khởi động tự động (Script)

```bash
python start_complete_system.py
```

Script này sẽ:
- Kiểm tra dependencies
- Khởi động PostgreSQL
- Khởi động Backend (port 5000) + Auto-start Chatbot (port 8001)
- Khởi động Frontend (port 3000)

### Phương pháp 2: Khởi động thủ công

1. **Khởi động PostgreSQL**
   ```bash
   # Đảm bảo PostgreSQL đang chạy
   # Database: Fbot_Management
   # User: postgres, Password: chiendz098
   ```

2. **Khởi động Backend (Auto-starts Chatbot)**
   ```bash
   cd backend
   npm install
   npm start
   # Chatbot sẽ tự động khởi động!
   ```

3. **Khởi động Frontend**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

### 🎯 Floating Chatbot Button

- **Vị trí**: Góc phải bên dưới màn hình
- **Hiển thị**: Chỉ khi user đã đăng nhập
- **Tính năng**: Click để mở/đóng chatbot
- **Animation**: Hiệu ứng đẹp mắt với ripple effect

## 🧪 Testing

### Chạy test tự động:
```bash
python test_chatbot_integration.py
```

### Test thủ công:

1. **Test RAG Agent:**
   - "Học phí ngành CNTT bao nhiêu?"
   - "Cho tôi xem danh sách lớp học"
   - "Thông tin về điều kiện tuyển sinh"

2. **Test Schedule Agent:**
   - "Tạo task học Python với độ ưu tiên cao"
   - "Xem danh sách todo của tôi"
   - "Tạo nhắc nhở nộp bài tập"

3. **Test Generic Agent:**
   - "Thời tiết hôm nay thế nào?"
   - "Tìm kiếm thông tin về AI"
   - "Xin chào, bạn có khỏe không?"

## 🔧 Cấu hình

### Environment Variables:

**Backend (.env):**
```
DB_URI=postgresql://postgres:chiendz098@localhost:5432/Fbot_Management
CHATBOT_API_URL=http://localhost:8001
```

**Chatbot (.env):**
```
GOOGLE_API_KEY=your_gemini_api_key
DB_URI=postgresql://postgres:chiendz098@localhost:5432/Fbot_Management
TAVILY_API_KEY=your_tavily_api_key
```

## 📊 Kiến trúc hệ thống

```
Frontend (Next.js) :3000
    ↓ API calls
Backend (Node.js) :5000 ←── Auto-starts Chatbot
    ↓ HTTP requests
Chatbot (FastAPI) :8001 ←── Auto-integrated
    ↓ Database queries
PostgreSQL :5432

🎯 Floating Button: Always visible in bottom-right corner
🤖 Multi-Agent System: RAG + Schedule + Generic agents
💾 Chat History: Persistent across sessions
🔄 Auto-Startup: Chatbot starts with backend
```

## 🎨 UI/UX Features

- **Agent Type Indicators**: Hiển thị loại agent đang xử lý
- **Enhanced Typing**: Animation typing indicator
- **Suggestions**: Gợi ý câu hỏi tiếp theo
- **Chat History**: Lưu trữ và load lịch sử chat
- **Clear History**: Xóa lịch sử chat
- **Session Management**: Quản lý phiên chat

## 🔍 Troubleshooting

### Chatbot không khởi động:
1. Kiểm tra Python dependencies: `pip install -r requirements.txt`
2. Kiểm tra Google API key trong .env
3. Kiểm tra port 8001 có bị chiếm không

### Backend không kết nối được chatbot:
1. Kiểm tra CHATBOT_API_URL trong backend .env
2. Kiểm tra chatbot health: `curl http://localhost:8001/health`
3. Kiểm tra firewall/antivirus

### Database connection issues:
1. Kiểm tra PostgreSQL đang chạy
2. Kiểm tra database credentials
3. Kiểm tra database "Fbot_Management" đã tồn tại

### Frontend không hiển thị chatbot:
1. Kiểm tra console browser có lỗi không
2. Kiểm tra API calls trong Network tab
3. Kiểm tra authentication

## 📝 Logs và Monitoring

- **Backend logs**: Console output khi chạy `npm start`
- **Chatbot logs**: Console output khi chạy `python start_chatbot.py`
- **Frontend logs**: Browser console
- **Health checks**: 
  - Backend: `http://localhost:3001/api/health`
  - Chatbot: `http://localhost:8001/health`

## 🔄 Cập nhật và Maintenance

### Cập nhật chatbot:
1. Sửa đổi agents trong `chatbot_moi/agents/`
2. Restart chatbot service
3. Test với `python test_chatbot_integration.py`

### Thêm agent mới:
1. Tạo agent trong `chatbot_moi/agents/`
2. Cập nhật router trong `graph.py`
3. Thêm tools nếu cần trong `tools.py`
4. Cập nhật prompts trong `prompts.py`

### Cập nhật database schema:
1. Tạo migration trong backend
2. Cập nhật tools trong chatbot nếu cần
3. Test integration

## 🎉 Kết luận

Hệ thống chatbot đã được tích hợp hoàn chỉnh với web application theo yêu cầu:

### ✅ **Đã hoàn thành:**
- **🚀 Auto-startup**: Chatbot tự động khởi động cùng backend (`npm start`)
- **🎯 Floating Button**: Nút chat ở góc phải bên dưới, luôn hiển thị
- **🤖 Multi-Agent**: RAG, Schedule, Generic agents hoạt động hoàn hảo
- **💾 Database Integration**: Tích hợp sâu với PostgreSQL
- **📱 Responsive UI**: Giao diện đẹp với animations
- **💬 Chat History**: Lưu trữ và quản lý lịch sử chat
- **🔄 Session Management**: Quản lý phiên chat thông minh

### 🎯 **Cách sử dụng đơn giản:**
1. `cd backend && npm start` - Chatbot tự động khởi động!
2. `cd frontend && npm run dev` - Mở web
3. Tìm nút chat ở góc phải bên dưới
4. Enjoy! 🎉

### 🌟 **Tính năng nổi bật:**
- **Always Available**: Chatbot có mặt trên mọi trang
- **Smart Routing**: Tự động chọn agent phù hợp
- **Todo Integration**: Quản lý task qua chat
- **School Info**: Trả lời thông tin trường học
- **Beautiful UI**: Giao diện hiện đại với animations

**Chatbot sẵn sàng phục vụ sinh viên và giảng viên với đầy đủ chức năng!** 🎓✨
