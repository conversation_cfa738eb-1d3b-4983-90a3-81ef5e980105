import { useEffect, useCallback, useRef, useState } from 'react';

/**
 * Performance optimization hooks for lightning fast website
 */

// Debounce hook for performance
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  
  const debouncedCallback = useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]) as T;
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return debouncedCallback;
}

// Throttle hook for performance
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now());
  
  const throttledCallback = useCallback((...args: Parameters<T>) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = Date.now();
    }
  }, [callback, delay]) as T;
  
  return throttledCallback;
}

// Intersection Observer for lazy loading
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );
    
    observer.observe(element);
    
    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, options]);
  
  return isIntersecting;
}

// Preload resources
export function usePreloadResources() {
  useEffect(() => {
    // Preload critical resources
    const preloadLink = (href: string, as: string) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = href;
      link.as = as;
      document.head.appendChild(link);
    };
    
    // Preload fonts
    preloadLink('/fonts/inter.woff2', 'font');
    
    // Preload critical images
    preloadLink('/images/hero-bg.webp', 'image');
    
    // DNS prefetch for external resources
    const dnsPrefetch = (href: string) => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = href;
      document.head.appendChild(link);
    };
    
    dnsPrefetch('//fonts.googleapis.com');
    dnsPrefetch('//api.openai.com');
  }, []);
}

// Optimize animations for performance
export function useOptimizedAnimation() {
  useEffect(() => {
    // Reduce motion for users who prefer it
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    if (mediaQuery.matches) {
      document.documentElement.style.setProperty('--animation-duration', '0.01ms');
    }
    
    // Optimize for 60fps
    const optimizeAnimations = () => {
      document.documentElement.style.setProperty('--animation-timing', 'cubic-bezier(0.4, 0, 0.2, 1)');
    };
    
    optimizeAnimations();
  }, []);
}

// Memory management
export function useMemoryOptimization() {
  useEffect(() => {
    const cleanup = () => {
      // Clear any large objects from memory
      if (window.gc) {
        window.gc();
      }
    };
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', cleanup);
    
    return () => {
      window.removeEventListener('beforeunload', cleanup);
      cleanup();
    };
  }, []);
}

// Performance monitoring
export function usePerformanceMonitoring() {
  useEffect(() => {
    // Monitor Core Web Vitals - simplified without external dependency
    if (typeof window !== 'undefined') {
      // Monitor navigation timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        console.log('Navigation timing:', {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          ttfb: navigation.responseStart - navigation.requestStart,
        });
      }
    }
    
    // Monitor performance
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          console.log('Navigation timing:', entry);
        }
      }
    });
    
    observer.observe({ entryTypes: ['navigation', 'paint'] });
    
    return () => {
      observer.disconnect();
    };
  }, []);
}

// Fast image loading
export function useFastImageLoading() {
  const loadImage = useCallback((src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = reject;
      img.src = src;
    });
  }, []);
  
  const preloadImages = useCallback(async (srcs: string[]) => {
    try {
      await Promise.all(srcs.map(loadImage));
    } catch (error) {
      console.warn('Failed to preload some images:', error);
    }
  }, [loadImage]);
  
  return { loadImage, preloadImages };
}

// Service Worker for caching
export function useServiceWorker() {
  useEffect(() => {
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    }
  }, []);
}

// Bundle all performance optimizations - simplified
export function usePerformanceOptimizations() {
  useEffect(() => {
    // Simple performance optimizations
    if (typeof window !== 'undefined') {
      // Preload critical resources
      const preloadFont = (href: string) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        link.href = href;
        document.head.appendChild(link);
      };

      // DNS prefetch
      const dnsPrefetch = (href: string) => {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = href;
        document.head.appendChild(link);
      };

      // Apply optimizations
      preloadFont('/fonts/inter-var.woff2');
      dnsPrefetch('//fonts.googleapis.com');

      // Optimize animations for reduced motion
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      if (mediaQuery.matches) {
        document.documentElement.style.setProperty('--animation-duration', '0.01ms');
      }
    }
  }, []);
}
