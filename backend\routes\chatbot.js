const express = require('express');
const router = express.Router();
const chatbotService = require('../services/chatbotService');
const ChatHistory = require('../models/ChatHistory');
const { authenticateToken } = require('../middleware/auth');
const { sanitizeInput } = require('../middleware/validation');

// Chat endpoint
router.post('/chat', authenticateToken, sanitizeInput, async (req, res) => {
  try {
    const { message, sessionId } = req.body;
    const userId = req.user.id;

    if (!message || message.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    // Call chatbot_moi API
    const chatbotResponse = await chatbotService.sendMessage(message, userId);

    if (chatbotResponse.success) {
      // Save to database
      await ChatHistory.create({
        userId,
        message: message.trim(),
        response: chatbotResponse.response,
        agentType: chatbotResponse.agentType,
        sessionId: sessionId || null
      });

      res.json({
        success: true,
        response: chatbotResponse.response,
        agentType: chatbotResponse.agentType
      });
    } else {
      res.status(500).json({
        success: false,
        message: chatbotResponse.response
      });
    }
  } catch (error) {
    console.error('Chatbot route error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get chat history
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 50, sessionId } = req.query;

    const whereClause = { userId };
    if (sessionId) {
      whereClause.sessionId = sessionId;
    }

    const history = await ChatHistory.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      history: history.reverse()
    });
  } catch (error) {
    console.error('Chat history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch chat history'
    });
  }
});

// Clear chat history
router.delete('/history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { sessionId } = req.query;

    const whereClause = { userId };
    if (sessionId) {
      whereClause.sessionId = sessionId;
    }

    await ChatHistory.destroy({
      where: whereClause
    });

    res.json({
      success: true,
      message: 'Chat history cleared successfully'
    });
  } catch (error) {
    console.error('Clear chat history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear chat history'
    });
  }
});

module.exports = router;