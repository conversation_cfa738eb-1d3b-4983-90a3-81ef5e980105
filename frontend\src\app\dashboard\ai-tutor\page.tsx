'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/lib/auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Brain, 
  Send, 
  Loader2, 
  User, 
  Bot,
  Sparkles,
  BookOpen,
  Target,
  Clock,
  Plus
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface LearningPathSuggestion {
  title: string;
  description: string;
  duration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export default function AITutorPage() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: `Hello ${user?.name}! 👋 I'm your AI tutor. I can help you with:

• Creating personalized learning paths
• Answering questions about your subjects
• Providing study tips and techniques
• Breaking down complex topics
• Generating actionable study plans

What would you like to learn about today?`,
      timestamp: new Date(),
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPathGenerator, setShowPathGenerator] = useState(false);
  const [pathPrompt, setPathPrompt] = useState('');
  const [pathSettings, setPathSettings] = useState<{
    duration: number;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    hoursPerDay: number;
  }>({
    duration: 30,
    difficulty: 'intermediate',
    hoursPerDay: 2,
  });
  const [isGeneratingPath, setIsGeneratingPath] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputMessage,
          context: {
            type: 'general',
            userProfile: {
              role: user?.role,
              name: user?.name,
            },
          },
        }),
      });

      const data = await response.json();

      if (data.success) {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: data.data.message,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, assistantMessage]);
      } else {
        toast.error(data.error || 'Failed to get AI response');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateLearningPath = async () => {
    if (!pathPrompt.trim() || isGeneratingPath) return;

    setIsGeneratingPath(true);

    try {
      const response = await fetch('/api/ai/generate-path', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: pathPrompt,
          duration: pathSettings.duration,
          difficulty: pathSettings.difficulty,
          hoursPerDay: pathSettings.hoursPerDay,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        setShowPathGenerator(false);
        setPathPrompt('');
        
        // Add a message about the generated path
        const pathMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `🎉 Great! I've created a personalized learning path for you: "${data.data.learningPath.title}". 

I've generated ${data.data.todosGenerated} daily tasks that will guide you through your learning journey. You can find them in your Todos section.

The learning path includes:
• ${data.data.learningPath.duration} days of structured learning
• Daily tasks tailored to your goals
• Resources and practice exercises
• Progress tracking

Would you like me to explain any part of the learning path or help you with something else?`,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, pathMessage]);
      } else {
        toast.error(data.error || 'Failed to generate learning path');
      }
    } catch (error) {
      console.error('Error generating learning path:', error);
      toast.error('Failed to generate learning path');
    } finally {
      setIsGeneratingPath(false);
    }
  };

  const quickPrompts = [
    "Help me create a study plan for web development",
    "Explain machine learning concepts for beginners",
    "How can I improve my programming skills?",
    "Create a learning path for data structures and algorithms",
    "What are the best study techniques for computer science?",
  ];

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full">
            <Brain className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            AI Tutor
          </h1>
        </div>
        <p className="text-gray-600">
          Your personal AI learning assistant powered by GPT-4o
        </p>
      </div>

      {/* Quick Actions */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setShowPathGenerator(true)}>
            <CardContent className="p-6 text-center">
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Target className="h-8 w-8 text-purple-600 mx-auto mb-3" />
              </motion.div>
              <h3 className="font-semibold mb-2 flex items-center justify-center gap-2">
                Generate Learning Path
                <Plus className="h-4 w-4 text-purple-500" />
              </h3>
              <p className="text-sm text-gray-600">
                Create a personalized study plan with AI-generated daily tasks
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardContent className="p-6 text-center">
              <motion.div
                animate={{ y: [0, -2, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
              >
                <BookOpen className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              </motion.div>
              <h3 className="font-semibold mb-2 flex items-center justify-center gap-2">
                Study Assistant
                <Clock className="h-4 w-4 text-blue-500" />
              </h3>
              <p className="text-sm text-gray-600">
                Get help with assignments, explanations, and study tips
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>

      {/* Chat Interface */}
      <Card className="h-[600px] flex flex-col">
        <CardHeader className="border-b">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-purple-600" />
            Chat with AI Tutor
          </CardTitle>
        </CardHeader>

        {/* Messages */}
        <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={cn(
                "flex gap-3",
                message.role === 'user' ? 'justify-end' : 'justify-start'
              )}
            >
              {message.role === 'assistant' && (
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Bot className="h-4 w-4 text-white" />
                </div>
              )}
              
              <div
                className={cn(
                  "max-w-[80%] rounded-lg px-4 py-2",
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                )}
              >
                <div className="whitespace-pre-wrap">{message.content}</div>
                <div className={cn(
                  "text-xs mt-1",
                  message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                )}>
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>

              {message.role === 'user' && (
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="h-4 w-4 text-gray-600" />
                </div>
              )}
            </div>
          ))}
          
          {isLoading && (
            <div className="flex gap-3 justify-start">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div className="bg-gray-100 rounded-lg px-4 py-2">
                <Loader2 className="h-4 w-4 animate-spin" />
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </CardContent>

        {/* Input */}
        <div className="border-t p-4">
          {/* Quick Prompts */}
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-2">Quick prompts:</p>
            <div className="flex flex-wrap gap-2">
              {quickPrompts.slice(0, 3).map((prompt, index) => (
                <button
                  key={index}
                  onClick={() => setInputMessage(prompt)}
                  className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded-full transition-colors"
                >
                  {prompt}
                </button>
              ))}
            </div>
          </div>

          <div className="flex gap-2">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Ask me anything about your studies..."
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              disabled={isLoading}
            />
            <Button 
              onClick={handleSendMessage} 
              disabled={isLoading || !inputMessage.trim()}
              size="icon"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </Card>

      {/* Learning Path Generator Dialog */}
      {showPathGenerator && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-purple-600" />
                Generate Learning Path
              </CardTitle>
              <CardDescription>
                Describe what you want to learn and I&apos;ll create a personalized study plan
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <textarea
                placeholder="E.g., I want to learn React.js from scratch, including hooks, state management, and building real projects..."
                value={pathPrompt}
                onChange={(e) => setPathPrompt(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"
                rows={4}
              />

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Duration (days)</label>
                  <Input
                    type="number"
                    min="1"
                    max="365"
                    value={pathSettings.duration}
                    onChange={(e) => setPathSettings(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Difficulty</label>
                  <select
                    value={pathSettings.difficulty}
                    onChange={(e) => setPathSettings(prev => ({ ...prev, difficulty: e.target.value as 'beginner' | 'intermediate' | 'advanced' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Hours/day</label>
                  <Input
                    type="number"
                    min="0.5"
                    max="8"
                    step="0.5"
                    value={pathSettings.hoursPerDay}
                    onChange={(e) => setPathSettings(prev => ({ ...prev, hoursPerDay: parseFloat(e.target.value) }))}
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowPathGenerator(false);
                    setPathPrompt('');
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleGenerateLearningPath}
                  disabled={isGeneratingPath || !pathPrompt.trim()}
                  className="flex-1"
                >
                  {isGeneratingPath ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Path
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
