import { toast } from 'react-hot-toast';
import { mockApi<PERSON>all, shouldUseMockApi } from './mock-api';
import {
  Create<PERSON><PERSON>Form,
  CreateTodoForm,
  UpdateTodoForm,
  CreateAssignmentForm,
  AiLearningPathRequest
} from '@/types';

// Additional types for API methods
interface PageData {
  title: string;
  content?: string;
  parentId?: string;
}

interface BlockData {
  type: string;
  content: string;
  pageId: string;
  order?: number;
}

interface WorkspaceData {
  name: string;
  description?: string;
}

interface EventData {
  title: string;
  description?: string;
  start: Date;
  end: Date;
  location?: string;
}

interface ForumPostData {
  title: string;
  content: string;
  categoryId?: string;
}

interface ForumCommentData {
  content: string;
}

interface ForumCategoryData {
  name: string;
  description?: string;
}

interface ExamData {
  title: string;
  description?: string;
  duration: number;
  questions: unknown[];
}

interface ResourceData {
  title: string;
  description?: string;
  type: string;
  url?: string;
  file?: File;
}

interface MeetingData {
  title: string;
  description?: string;
  scheduledAt: Date;
  duration: number;
}

interface ProgressData {
  userId: string;
  activityType: string;
  value: number;
  metadata?: Record<string, unknown>;
}

interface SharePermissions {
  userId: string;
  permission: 'read' | 'write' | 'admin';
}

interface FeedbackData {
  type: string;
  subject: string;
  message: string;
  rating?: number;
}

// Complete API Client for FPT UniHub Backend Integration
const IS_DEMO_MODE = process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_API_URL;

interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface ApiOptions extends RequestInit {
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
  successMessage?: string;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: ApiOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      showErrorToast = true,
      showSuccessToast = false,
      successMessage,
      ...fetchOptions
    } = options;

    // Demo mode or backend unavailable - return mock data
    if (IS_DEMO_MODE || shouldUseMockApi()) {
      try {
        const mockData = await mockApiCall(endpoint, fetchOptions);
        if (showSuccessToast && (successMessage)) {
          toast.success(successMessage || 'Operation successful');
        }
        return {
          success: true,
          data: mockData as T,
          message: 'Mock API - operation simulated'
        };
      } catch (error) {
        if (showErrorToast) {
          toast.error('Mock API error');
        }
        return {
          success: false,
          error: 'Mock API error'
        };
      }
    }

    const url = `${this.baseUrl}${endpoint}`;

    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...fetchOptions.headers,
      },
      ...fetchOptions,
    };

    try {
      const response = await fetch(url, defaultOptions);

      // Handle empty response
      const responseText = await response.text();
      let data: ApiResponse<T>;

      try {
        data = responseText ? JSON.parse(responseText) : { success: true, data: null as T };
      } catch (parseError) {
        console.error('JSON parse error:', parseError, 'Response text:', responseText);
        data = {
          success: false,
          error: 'Invalid JSON response from server'
        };
      }

      if (!response.ok) {
        if (showErrorToast) {
          toast.error(data.error || `HTTP ${response.status}: ${response.statusText}`);
        }
        return data;
      }

      if (showSuccessToast && (successMessage || data.message)) {
        toast.success(successMessage || data.message || 'Operation successful');
      }

      return data;
    } catch (error) {
      // Fallback to mock API on network error
      console.log(`🔄 Network error, falling back to mock API for: ${endpoint}`, error);
      try {
        const mockData = await mockApiCall(endpoint, fetchOptions);
        if (showSuccessToast && successMessage) {
          toast.success(successMessage);
        }
        return {
          success: true,
          data: mockData as T,
          message: 'Fallback to mock API due to network error'
        };
      } catch (mockError) {
        const errorMessage = error instanceof Error ? error.message : 'Network error';
        if (showErrorToast) {
          toast.error(errorMessage);
        }
        return {
          success: false,
          error: errorMessage,
        };
      }
    }
  }

  // GET request
  async get<T>(endpoint: string, options?: ApiOptions): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  // POST request
  async post<T>(
    endpoint: string,
    data?: unknown,
    options?: ApiOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT request
  async put<T>(
    endpoint: string,
    data?: unknown,
    options?: ApiOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PATCH request
  async patch<T>(
    endpoint: string,
    data?: unknown,
    options?: ApiOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE request
  async delete<T>(endpoint: string, options?: ApiOptions): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  // Upload file
  async upload<T>(
    endpoint: string,
    formData: FormData,
    options?: Omit<ApiOptions, 'headers'>
  ): Promise<ApiResponse<T>> {
    const { showErrorToast = true, showSuccessToast = false, ...fetchOptions } = options || {};

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        body: formData,
        ...fetchOptions,
      });

      const data: ApiResponse<T> = await response.json();

      if (!response.ok) {
        if (showErrorToast) {
          toast.error(data.error || `Upload failed: ${response.statusText}`);
        }
        return data;
      }

      if (showSuccessToast && data.message) {
        toast.success(data.message);
      }

      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      if (showErrorToast) {
        toast.error(errorMessage);
      }
      return {
        success: false,
        error: errorMessage,
      };
    }
  }
}

// Create singleton instance
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';
export const apiClient = new ApiClient(API_BASE_URL);

// Specific API methods
export const dashboardApi = {
  getStats: () => apiClient.get('/dashboard/stats'),
  refreshStats: () => apiClient.get('/dashboard/stats', { 
    showSuccessToast: true, 
    successMessage: 'Dashboard refreshed!' 
  }),
};

export const classesApi = {
  getAll: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/classes${query}`);
  },
  getById: (id: string) => apiClient.get(`/classes/${id}`),
  create: (data: CreateClassForm) => apiClient.post('/classes', data, {
    showSuccessToast: true,
    successMessage: 'Class created successfully!'
  }),
  update: (id: string, data: Partial<CreateClassForm>) => apiClient.put(`/classes/${id}`, data, {
    showSuccessToast: true,
    successMessage: 'Class updated successfully!'
  }),
  delete: (id: string) => apiClient.delete(`/classes/${id}`, { 
    showSuccessToast: true, 
    successMessage: 'Class deleted successfully!' 
  }),
  join: (inviteCode: string) => apiClient.post('/classes/join', { inviteCode }, { 
    showSuccessToast: true 
  }),
};

export const todosApi = {
  getAll: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/todos${query}`);
  },
  getById: (id: string) => apiClient.get(`/todos/${id}`),
  create: (data: CreateTodoForm) => apiClient.post('/todos', data, {
    showSuccessToast: true,
    successMessage: 'Todo created successfully!'
  }),
  update: (id: string, data: UpdateTodoForm) => apiClient.put(`/todos/${id}`, data),
  delete: (id: string) => apiClient.delete(`/todos/${id}`, { 
    showSuccessToast: true, 
    successMessage: 'Todo deleted successfully!' 
  }),
  reorder: (todoIds: string[]) => apiClient.patch('/todos/reorder', { todoIds }),
};

export const assignmentsApi = {
  getAll: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/assignments${query}`);
  },
  getById: (id: string) => apiClient.get(`/assignments/${id}`),
  create: (data: CreateAssignmentForm) => apiClient.post('/assignments', data, {
    showSuccessToast: true,
    successMessage: 'Assignment created successfully!'
  }),
  update: (id: string, data: Partial<CreateAssignmentForm>) => apiClient.put(`/assignments/${id}`, data, {
    showSuccessToast: true,
    successMessage: 'Assignment updated successfully!'
  }),
  delete: (id: string) => apiClient.delete(`/assignments/${id}`, { 
    showSuccessToast: true, 
    successMessage: 'Assignment deleted successfully!' 
  }),
};

export const aiApi = {
  chat: (message: string, context?: Record<string, unknown>) => apiClient.post('/ai/chat', { message, context }),
  generatePath: (data: AiLearningPathRequest) => apiClient.post('/ai/generate-path', data, {
    showSuccessToast: true
  }),
};

export const notificationsApi = {
  getAll: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/notifications${query}`);
  },
  markAsRead: (notificationIds?: string[], markAllAsRead?: boolean) =>
    apiClient.patch('/notifications', { notificationIds, markAllAsRead }),
  delete: (notificationIds?: string[], deleteAll?: boolean) =>
    apiClient.delete('/notifications', {
      body: JSON.stringify({ notificationIds, deleteAll }),
      headers: { 'Content-Type': 'application/json' }
    }),
};

// Pages API - Complete Notion-style page management
export const pagesApi = {
  getAll: (workspaceId?: string, params?: Record<string, string>) => {
    const query = new URLSearchParams(params || {});
    if (workspaceId) query.set('workspaceId', workspaceId);
    return apiClient.get(`/pages?${query}`);
  },
  getById: (id: string) => apiClient.get(`/pages/${id}`),
  create: (data: PageData) => apiClient.post('/pages', data, {
    showSuccessToast: true,
    successMessage: 'Page created successfully!'
  }),
  update: (id: string, data: Partial<PageData>) => apiClient.put(`/pages/${id}`, data),
  delete: (id: string) => apiClient.delete(`/pages/${id}`, {
    showSuccessToast: true,
    successMessage: 'Page deleted successfully!'
  }),
  duplicate: (id: string) => apiClient.post(`/pages/${id}/duplicate`, {}, {
    showSuccessToast: true,
    successMessage: 'Page duplicated successfully!'
  }),
  move: (id: string, parentId: string | null) => apiClient.patch(`/pages/${id}/move`, { parentId }),
  getChildren: (id: string) => apiClient.get(`/pages/${id}/children`),
  search: (query: string, workspaceId?: string) => {
    const params = new URLSearchParams({ q: query });
    if (workspaceId) params.set('workspaceId', workspaceId);
    return apiClient.get(`/pages/search?${params}`);
  },
  export: (id: string, format: 'markdown' | 'html' | 'pdf') =>
    apiClient.get(`/pages/${id}/export?format=${format}`),
  getHistory: (id: string) => apiClient.get(`/pages/${id}/history`),
  restore: (id: string, versionId: string) => apiClient.post(`/pages/${id}/restore`, { versionId }),
};

// Blocks API - Complete block management for editor
export const blocksApi = {
  getByPageId: (pageId: string) => apiClient.get(`/blocks?pageId=${pageId}`),
  getById: (id: string) => apiClient.get(`/blocks/${id}`),
  create: (data: BlockData) => apiClient.post('/blocks', data),
  update: (id: string, data: Partial<BlockData>) => apiClient.put(`/blocks/${id}`, data),
  delete: (id: string) => apiClient.delete(`/blocks/${id}`),
  reorder: (pageId: string, blockIds: string[]) =>
    apiClient.patch('/blocks/reorder', { pageId, blockIds }),
  duplicate: (id: string) => apiClient.post(`/blocks/${id}/duplicate`),
  convert: (id: string, newType: string) => apiClient.patch(`/blocks/${id}/convert`, { type: newType }),
};

// Workspaces API - Complete workspace management
export const workspacesApi = {
  getAll: () => apiClient.get('/workspaces'),
  getById: (id: string) => apiClient.get(`/workspaces/${id}`),
  create: (data: WorkspaceData) => apiClient.post('/workspaces', data, {
    showSuccessToast: true,
    successMessage: 'Workspace created successfully!'
  }),
  update: (id: string, data: Partial<WorkspaceData>) => apiClient.put(`/workspaces/${id}`, data, {
    showSuccessToast: true,
    successMessage: 'Workspace updated successfully!'
  }),
  delete: (id: string) => apiClient.delete(`/workspaces/${id}`, {
    showSuccessToast: true,
    successMessage: 'Workspace deleted successfully!'
  }),
  getMembers: (id: string) => apiClient.get(`/workspaces/${id}/members`),
  addMember: (id: string, email: string, role: string) =>
    apiClient.post(`/workspaces/${id}/members`, { email, role }, {
      showSuccessToast: true,
      successMessage: 'Member added successfully!'
    }),
  removeMember: (id: string, userId: string) =>
    apiClient.delete(`/workspaces/${id}/members/${userId}`, {
      showSuccessToast: true,
      successMessage: 'Member removed successfully!'
    }),
  updateMemberRole: (id: string, userId: string, role: string) =>
    apiClient.patch(`/workspaces/${id}/members/${userId}`, { role }),
  getInvites: (id: string) => apiClient.get(`/workspaces/${id}/invites`),
  createInvite: (id: string, email: string, role: string) =>
    apiClient.post(`/workspaces/${id}/invites`, { email, role }),
  acceptInvite: (token: string) => apiClient.post('/workspaces/invites/accept', { token }),
};

// AI Orchestrator API - Complete AI services
export const aiOrchestratorApi = {
  chat: (message: string, context?: Record<string, unknown>) => apiClient.post('/ai/orchestrator/chat', { message, context }),
  generateContent: (type: string, prompt: string, context?: Record<string, unknown>) =>
    apiClient.post('/ai/orchestrator/generate', { type, prompt, context }),
  summarize: (content: string) => apiClient.post('/ai/orchestrator/summarize', { content }),
  translate: (content: string, targetLanguage: string) =>
    apiClient.post('/ai/orchestrator/translate', { content, targetLanguage }),
  explain: (content: string, level: 'beginner' | 'intermediate' | 'advanced') =>
    apiClient.post('/ai/orchestrator/explain', { content, level }),
  generateQuiz: (content: string, questionCount: number) =>
    apiClient.post('/ai/orchestrator/quiz', { content, questionCount }),
  generateLearningPath: (subject: string, level: string, duration: string, goals: string[]) =>
    apiClient.post('/ai/orchestrator/learning-path', { subject, level, duration, goals }),
  analyzeProgress: (userId: string, timeframe: string) =>
    apiClient.get(`/ai/orchestrator/progress/${userId}?timeframe=${timeframe}`),
  getSuggestions: (userId: string, context?: Record<string, unknown>) =>
    apiClient.post('/ai/orchestrator/suggestions', { userId, context }),
};

// Events API - Complete event management
export const eventsApi = {
  getAll: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/events${query}`);
  },
  getById: (id: string) => apiClient.get(`/events/${id}`),
  create: (data: EventData) => apiClient.post('/events', data, {
    showSuccessToast: true,
    successMessage: 'Event created successfully!'
  }),
  update: (id: string, data: Partial<EventData>) => apiClient.put(`/events/${id}`, data, {
    showSuccessToast: true,
    successMessage: 'Event updated successfully!'
  }),
  delete: (id: string) => apiClient.delete(`/events/${id}`, {
    showSuccessToast: true,
    successMessage: 'Event deleted successfully!'
  }),
  register: (id: string) => apiClient.post(`/events/${id}/register`, {}, {
    showSuccessToast: true,
    successMessage: 'Registered for event successfully!'
  }),
  unregister: (id: string) => apiClient.delete(`/events/${id}/register`, {
    showSuccessToast: true,
    successMessage: 'Unregistered from event successfully!'
  }),
  getAttendees: (id: string) => apiClient.get(`/events/${id}/attendees`),
  checkIn: (id: string, userId: string) => apiClient.post(`/events/${id}/checkin`, { userId }),
};

// Forum API - Complete forum system
export const forumApi = {
  getPosts: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/forum/posts${query}`);
  },
  getPostById: (id: string) => apiClient.get(`/forum/posts/${id}`),
  createPost: (data: any) => apiClient.post('/forum/posts', data, {
    showSuccessToast: true,
    successMessage: 'Post created successfully!'
  }),
  updatePost: (id: string, data: any) => apiClient.put(`/forum/posts/${id}`, data),
  deletePost: (id: string) => apiClient.delete(`/forum/posts/${id}`, {
    showSuccessToast: true,
    successMessage: 'Post deleted successfully!'
  }),
  likePost: (id: string) => apiClient.post(`/forum/posts/${id}/like`),
  unlikePost: (id: string) => apiClient.delete(`/forum/posts/${id}/like`),
  getComments: (postId: string) => apiClient.get(`/forum/posts/${postId}/comments`),
  createComment: (postId: string, data: any) => apiClient.post(`/forum/posts/${postId}/comments`, data),
  updateComment: (postId: string, commentId: string, data: any) =>
    apiClient.put(`/forum/posts/${postId}/comments/${commentId}`, data),
  deleteComment: (postId: string, commentId: string) =>
    apiClient.delete(`/forum/posts/${postId}/comments/${commentId}`),
  getCategories: () => apiClient.get('/forum/categories'),
  createCategory: (data: any) => apiClient.post('/forum/categories', data),
};

// Exams API - Complete exam system
export const examsApi = {
  getAll: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/exams${query}`);
  },
  getById: (id: string) => apiClient.get(`/exams/${id}`),
  create: (data: any) => apiClient.post('/exams', data, {
    showSuccessToast: true,
    successMessage: 'Exam created successfully!'
  }),
  update: (id: string, data: any) => apiClient.put(`/exams/${id}`, data),
  delete: (id: string) => apiClient.delete(`/exams/${id}`, {
    showSuccessToast: true,
    successMessage: 'Exam deleted successfully!'
  }),
  startExam: (id: string) => apiClient.post(`/exams/${id}/start`),
  submitExam: (id: string, answers: any) => apiClient.post(`/exams/${id}/submit`, { answers }),
  getResults: (id: string) => apiClient.get(`/exams/${id}/results`),
  getAttempts: (id: string) => apiClient.get(`/exams/${id}/attempts`),
};

// Resources API - Complete resource management
export const resourcesApi = {
  getAll: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/resources${query}`);
  },
  getById: (id: string) => apiClient.get(`/resources/${id}`),
  create: (data: any) => apiClient.post('/resources', data, {
    showSuccessToast: true,
    successMessage: 'Resource created successfully!'
  }),
  update: (id: string, data: any) => apiClient.put(`/resources/${id}`, data),
  delete: (id: string) => apiClient.delete(`/resources/${id}`, {
    showSuccessToast: true,
    successMessage: 'Resource deleted successfully!'
  }),
  download: (id: string) => apiClient.get(`/resources/${id}/download`),
  getCategories: () => apiClient.get('/resources/categories'),
  search: (query: string, filters?: any) => {
    const params = new URLSearchParams({ q: query });
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.set(key, String(value));
      });
    }
    return apiClient.get(`/resources/search?${params}`);
  },
};

// Mentor System API
export const mentorApi = {
  getMentors: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/mentors${query}`);
  },
  getMentorById: (id: string) => apiClient.get(`/mentors/${id}`),
  requestMentorship: (mentorId: string, message: string) =>
    apiClient.post('/mentors/request', { mentorId, message }, {
      showSuccessToast: true,
      successMessage: 'Mentorship request sent!'
    }),
  acceptRequest: (requestId: string) => apiClient.post(`/mentors/requests/${requestId}/accept`),
  rejectRequest: (requestId: string) => apiClient.post(`/mentors/requests/${requestId}/reject`),
  getRequests: () => apiClient.get('/mentors/requests'),
  getMentorships: () => apiClient.get('/mentors/mentorships'),
  scheduleMeeting: (mentorshipId: string, data: any) =>
    apiClient.post(`/mentors/mentorships/${mentorshipId}/meetings`, data),
  getMeetings: (mentorshipId: string) => apiClient.get(`/mentors/mentorships/${mentorshipId}/meetings`),
};

// Progress Tracking API
export const progressApi = {
  getOverview: (userId?: string, timeframe?: string) => {
    const params = new URLSearchParams();
    if (userId) params.set('userId', userId);
    if (timeframe) params.set('timeframe', timeframe);
    return apiClient.get(`/progress/overview?${params}`);
  },
  getDetailedProgress: (userId: string, subject?: string) => {
    const params = new URLSearchParams();
    if (subject) params.set('subject', subject);
    return apiClient.get(`/progress/detailed/${userId}?${params}`);
  },
  updateProgress: (data: any) => apiClient.post('/progress/update', data),
  getAchievements: (userId: string) => apiClient.get(`/progress/achievements/${userId}`),
  getLeaderboard: (timeframe?: string, category?: string) => {
    const params = new URLSearchParams();
    if (timeframe) params.set('timeframe', timeframe);
    if (category) params.set('category', category);
    return apiClient.get(`/progress/leaderboard?${params}`);
  },
  getStudyStreak: (userId: string) => apiClient.get(`/progress/streak/${userId}`),
  logStudyTime: (duration: number, subject?: string) =>
    apiClient.post('/progress/study-time', { duration, subject }),
};

// File Manager API
export const filesApi = {
  upload: (file: File, folder?: string) => {
    const formData = new FormData();
    formData.append('file', file);
    if (folder) formData.append('folder', folder);

    return apiClient.post('/files/upload', formData, {
      headers: {},
      showSuccessToast: true,
      successMessage: 'File uploaded successfully!'
    });
  },
  getAll: (folder?: string) => {
    const params = folder ? `?folder=${folder}` : '';
    return apiClient.get(`/files${params}`);
  },
  getById: (id: string) => apiClient.get(`/files/${id}`),
  delete: (id: string) => apiClient.delete(`/files/${id}`, {
    showSuccessToast: true,
    successMessage: 'File deleted successfully!'
  }),
  createFolder: (name: string, parentId?: string) =>
    apiClient.post('/files/folders', { name, parentId }),
  moveFile: (id: string, folderId: string) =>
    apiClient.patch(`/files/${id}/move`, { folderId }),
  shareFile: (id: string, permissions: any) =>
    apiClient.post(`/files/${id}/share`, permissions),
};

// Calendar Sync API
export const calendarApi = {
  getEvents: (start: string, end: string) =>
    apiClient.get(`/calendar/events?start=${start}&end=${end}`),
  createEvent: (data: any) => apiClient.post('/calendar/events', data),
  updateEvent: (id: string, data: any) => apiClient.put(`/calendar/events/${id}`, data),
  deleteEvent: (id: string) => apiClient.delete(`/calendar/events/${id}`),
  syncWithGoogle: (authCode: string) => apiClient.post('/calendar/sync/google', { authCode }),
  syncWithOutlook: (authCode: string) => apiClient.post('/calendar/sync/outlook', { authCode }),
  getIntegrations: () => apiClient.get('/calendar/integrations'),
  removeIntegration: (provider: string) => apiClient.delete(`/calendar/integrations/${provider}`),
};

// Feedback System API
export const feedbackApi = {
  getAll: (params?: Record<string, string>) => {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return apiClient.get(`/feedback${query}`);
  },
  create: (data: any) => apiClient.post('/feedback', data, {
    showSuccessToast: true,
    successMessage: 'Feedback submitted successfully!'
  }),
  update: (id: string, data: any) => apiClient.put(`/feedback/${id}`, data),
  delete: (id: string) => apiClient.delete(`/feedback/${id}`),
  respond: (id: string, response: string) =>
    apiClient.post(`/feedback/${id}/respond`, { response }),
  getStats: () => apiClient.get('/feedback/stats'),
};

// Trial Course API
export const trialCourseApi = {
  getAll: () => apiClient.get('/trial-courses'),
  getById: (id: string) => apiClient.get(`/trial-courses/${id}`),
  enroll: (id: string) => apiClient.post(`/trial-courses/${id}/enroll`, {}, {
    showSuccessToast: true,
    successMessage: 'Enrolled in trial course successfully!'
  }),
  getProgress: (id: string) => apiClient.get(`/trial-courses/${id}/progress`),
  completeLesson: (courseId: string, lessonId: string) =>
    apiClient.post(`/trial-courses/${courseId}/lessons/${lessonId}/complete`),
  submitQuiz: (courseId: string, quizId: string, answers: any) =>
    apiClient.post(`/trial-courses/${courseId}/quizzes/${quizId}/submit`, { answers }),
};

export default apiClient;
