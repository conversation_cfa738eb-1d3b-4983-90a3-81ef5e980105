// Global type declarations

declare module 'lucide-react' {
  import { ComponentType, SVGProps } from 'react';
  
  interface IconProps extends SVGProps<SVGSVGElement> {
    size?: number | string;
    strokeWidth?: number | string;
  }
  
  export const BookOpen: ComponentType<IconProps>;
  export const Mail: ComponentType<IconProps>;
  export const Lock: ComponentType<IconProps>;
  export const Eye: ComponentType<IconProps>;
  export const EyeOff: ComponentType<IconProps>;
  export const Loader2: ComponentType<IconProps>;
  export const GraduationCap: ComponentType<IconProps>;
  export const User: ComponentType<IconProps>;
  export const Plus: ComponentType<IconProps>;
  export const Check: ComponentType<IconProps>;
  export const X: ComponentType<IconProps>;
  export const Edit2: ComponentType<IconProps>;
  export const Calendar: ComponentType<IconProps>;
  export const Flag: ComponentType<IconProps>;
  export const Clock: ComponentType<IconProps>;
  export const Trash2: ComponentType<IconProps>;
  export const GripVertical: ComponentType<IconProps>;
  export const Users: ComponentType<IconProps>;
  export const MoreVertical: ComponentType<IconProps>;
  export const Settings: ComponentType<IconProps>;
  export const Search: ComponentType<IconProps>;
  export const ChevronRight: ComponentType<IconProps>;
  export const Circle: ComponentType<IconProps>;
  export const Moon: ComponentType<IconProps>;
  export const Sun: ComponentType<IconProps>;
  export const CheckCircle: ComponentType<IconProps>;
  export const AlertCircle: ComponentType<IconProps>;
  export const Info: ComponentType<IconProps>;
  export const AlertTriangle: ComponentType<IconProps>;
  export const Bell: ComponentType<IconProps>;
  export const Wifi: ComponentType<IconProps>;
  export const WifiOff: ComponentType<IconProps>;
  export const Send: ComponentType<IconProps>;
  export const Bot: ComponentType<IconProps>;
  export const Sparkles: ComponentType<IconProps>;
  export const Minimize2: ComponentType<IconProps>;
  export const Maximize2: ComponentType<IconProps>;
  export const MessageCircle: ComponentType<IconProps>;
  export const Play: ComponentType<IconProps>;
  export const Pause: ComponentType<IconProps>;
  export const RotateCcw: ComponentType<IconProps>;
  export const SkipForward: ComponentType<IconProps>;
  export const Volume2: ComponentType<IconProps>;
  export const VolumeX: ComponentType<IconProps>;
  export const Target: ComponentType<IconProps>;
  export const TrendingUp: ComponentType<IconProps>;
  export const Award: ComponentType<IconProps>;
  export const Brain: ComponentType<IconProps>;
  export const Lightbulb: ComponentType<IconProps>;
  export const Zap: ComponentType<IconProps>;
  export const Star: ComponentType<IconProps>;
  export const Heart: ComponentType<IconProps>;
  export const Share2: ComponentType<IconProps>;
  export const Download: ComponentType<IconProps>;
  export const Upload: ComponentType<IconProps>;
  export const FileText: ComponentType<IconProps>;
  export const Image: ComponentType<IconProps>;
  export const Video: ComponentType<IconProps>;
  export const Music: ComponentType<IconProps>;
  export const Code: ComponentType<IconProps>;
  export const Database: ComponentType<IconProps>;
  export const Server: ComponentType<IconProps>;
  export const Globe: ComponentType<IconProps>;
  export const Link: ComponentType<IconProps>;
  export const ExternalLink: ComponentType<IconProps>;
  export const Copy: ComponentType<IconProps>;
  export const UserPlus: ComponentType<IconProps>;
  export const LogOut: ComponentType<IconProps>;
  export const Menu: ComponentType<IconProps>;
  export const Home: ComponentType<IconProps>;
  export const Dashboard: ComponentType<IconProps>;
  export const BarChart3: ComponentType<IconProps>;
  export const PieChart: ComponentType<IconProps>;
  export const Activity: ComponentType<IconProps>;
  export const Bookmark: ComponentType<IconProps>;
  export const Filter: ComponentType<IconProps>;
  export const SortAsc: ComponentType<IconProps>;
  export const SortDesc: ComponentType<IconProps>;
  export const ChevronDown: ComponentType<IconProps>;
  export const ChevronUp: ComponentType<IconProps>;
  export const ChevronLeft: ComponentType<IconProps>;
  export const ArrowRight: ComponentType<IconProps>;
  export const ArrowLeft: ComponentType<IconProps>;
  export const ArrowUp: ComponentType<IconProps>;
  export const ArrowDown: ComponentType<IconProps>;
  export const RefreshCw: ComponentType<IconProps>;
  export const Save: ComponentType<IconProps>;
  export const Folder: ComponentType<IconProps>;
  export const FolderOpen: ComponentType<IconProps>;
  export const File: ComponentType<IconProps>;
  export const Archive: ComponentType<IconProps>;
  export const Package: ComponentType<IconProps>;
  export const Layers: ComponentType<IconProps>;
  export const Grid: ComponentType<IconProps>;
  export const List: ComponentType<IconProps>;
  export const Layout: ComponentType<IconProps>;
  export const Sidebar: ComponentType<IconProps>;
  export const Maximize: ComponentType<IconProps>;
  export const Minimize: ComponentType<IconProps>;
  export const FullScreen: ComponentType<IconProps>;
  export const ExitFullScreen: ComponentType<IconProps>;
  export const Expand: ComponentType<IconProps>;
  export const Shrink: ComponentType<IconProps>;
  export const Move: ComponentType<IconProps>;
  export const Resize: ComponentType<IconProps>;
  export const Rotate: ComponentType<IconProps>;
  export const FlipHorizontal: ComponentType<IconProps>;
  export const FlipVertical: ComponentType<IconProps>;
  export const Crop: ComponentType<IconProps>;
  export const Scissors: ComponentType<IconProps>;
  export const Clipboard: ComponentType<IconProps>;
  export const ClipboardCopy: ComponentType<IconProps>;
  export const ClipboardPaste: ComponentType<IconProps>;
  export const ClipboardList: ComponentType<IconProps>;
  export const ClipboardCheck: ComponentType<IconProps>;
  export const ClipboardX: ComponentType<IconProps>;
  export const Undo: ComponentType<IconProps>;
  export const Redo: ComponentType<IconProps>;
  export const History: ComponentType<IconProps>;
  export const Timer: ComponentType<IconProps>;
  export const Stopwatch: ComponentType<IconProps>;
  export const AlarmClock: ComponentType<IconProps>;
  export const Watch: ComponentType<IconProps>;
  export const Hourglass: ComponentType<IconProps>;
  export const CalendarDays: ComponentType<IconProps>;
  export const CalendarCheck: ComponentType<IconProps>;
  export const CalendarX: ComponentType<IconProps>;
  export const CalendarPlus: ComponentType<IconProps>;
  export const CalendarMinus: ComponentType<IconProps>;
  export const CalendarRange: ComponentType<IconProps>;
  export const CalendarClock: ComponentType<IconProps>;
  export const CalendarHeart: ComponentType<IconProps>;
  export const CalendarSearch: ComponentType<IconProps>;
  export const Smartphone: ComponentType<IconProps>;
  export const Tablet: ComponentType<IconProps>;
  export const Laptop: ComponentType<IconProps>;
  export const Monitor: ComponentType<IconProps>;
  export const Tv: ComponentType<IconProps>;
  export const Speaker: ComponentType<IconProps>;
  export const Headphones: ComponentType<IconProps>;
  export const Mic: ComponentType<IconProps>;
  export const MicOff: ComponentType<IconProps>;
  export const Camera: ComponentType<IconProps>;
  export const CameraOff: ComponentType<IconProps>;
  export const Webcam: ComponentType<IconProps>;
  export const Printer: ComponentType<IconProps>;
  export const Scanner: ComponentType<IconProps>;
  export const Keyboard: ComponentType<IconProps>;
  export const Mouse: ComponentType<IconProps>;
  export const Gamepad: ComponentType<IconProps>;
  export const Joystick: ComponentType<IconProps>;
  export const Controller: ComponentType<IconProps>;
  export const Cpu: ComponentType<IconProps>;
  export const HardDrive: ComponentType<IconProps>;
  export const MemoryStick: ComponentType<IconProps>;
  export const Usb: ComponentType<IconProps>;
  export const Bluetooth: ComponentType<IconProps>;
  export const Wifi1: ComponentType<IconProps>;
  export const Wifi2: ComponentType<IconProps>;
  export const Wifi3: ComponentType<IconProps>;
  export const Signal: ComponentType<IconProps>;
  export const SignalHigh: ComponentType<IconProps>;
  export const SignalLow: ComponentType<IconProps>;
  export const SignalMedium: ComponentType<IconProps>;
  export const SignalZero: ComponentType<IconProps>;
  export const Antenna: ComponentType<IconProps>;
  export const Radio: ComponentType<IconProps>;
  export const Satellite: ComponentType<IconProps>;
  export const Router: ComponentType<IconProps>;
  export const Network: ComponentType<IconProps>;
  export const Cloud: ComponentType<IconProps>;
  export const CloudDownload: ComponentType<IconProps>;
  export const CloudUpload: ComponentType<IconProps>;
  export const CloudSync: ComponentType<IconProps>;
  export const CloudOff: ComponentType<IconProps>;
  export const CloudRain: ComponentType<IconProps>;
  export const CloudSnow: ComponentType<IconProps>;
  export const CloudSun: ComponentType<IconProps>;
  export const CloudMoon: ComponentType<IconProps>;
  export const CloudLightning: ComponentType<IconProps>;
  export const CloudDrizzle: ComponentType<IconProps>;
  export const CloudHail: ComponentType<IconProps>;
  export const Umbrella: ComponentType<IconProps>;
  export const Thermometer: ComponentType<IconProps>;
  export const ThermometerSun: ComponentType<IconProps>;
  export const ThermometerSnowflake: ComponentType<IconProps>;
  export const Snowflake: ComponentType<IconProps>;
  export const Droplets: ComponentType<IconProps>;
  export const Wind: ComponentType<IconProps>;
  export const Tornado: ComponentType<IconProps>;
  export const Hurricane: ComponentType<IconProps>;
  export const Sunrise: ComponentType<IconProps>;
  export const Sunset: ComponentType<IconProps>;
  export const SunMedium: ComponentType<IconProps>;
  export const SunDim: ComponentType<IconProps>;
  export const MoonStar: ComponentType<IconProps>;
  export const Stars: ComponentType<IconProps>;
  export const Sparkle: ComponentType<IconProps>;
  export const Comet: ComponentType<IconProps>;
  export const Meteor: ComponentType<IconProps>;
  export const Planet: ComponentType<IconProps>;
  export const Rocket: ComponentType<IconProps>;
  export const Satellite2: ComponentType<IconProps>;
  export const Telescope: ComponentType<IconProps>;
  export const Microscope: ComponentType<IconProps>;
  export const Atom: ComponentType<IconProps>;
  export const Dna: ComponentType<IconProps>;
  export const Molecule: ComponentType<IconProps>;
  export const Flask: ComponentType<IconProps>;
  export const TestTube: ComponentType<IconProps>;
  export const Beaker: ComponentType<IconProps>;
  export const Pipette: ComponentType<IconProps>;
  export const Syringe: ComponentType<IconProps>;
  export const Pill: ComponentType<IconProps>;
  export const Capsule: ComponentType<IconProps>;
  export const Stethoscope: ComponentType<IconProps>;
  export const Bandage: ComponentType<IconProps>;
  export const FirstAid: ComponentType<IconProps>;
  export const Cross: ComponentType<IconProps>;
  export const Plus2: ComponentType<IconProps>;
  export const Minus: ComponentType<IconProps>;
  export const Equal: ComponentType<IconProps>;
  export const Divide: ComponentType<IconProps>;
  export const Multiply: ComponentType<IconProps>;
  export const Percent: ComponentType<IconProps>;
  export const Hash: ComponentType<IconProps>;
  export const AtSign: ComponentType<IconProps>;
  export const Dollar: ComponentType<IconProps>;
  export const Euro: ComponentType<IconProps>;
  export const Pound: ComponentType<IconProps>;
  export const Yen: ComponentType<IconProps>;
  export const Bitcoin: ComponentType<IconProps>;
  export const CreditCard: ComponentType<IconProps>;
  export const Wallet: ComponentType<IconProps>;
  export const Banknote: ComponentType<IconProps>;
  export const Coins: ComponentType<IconProps>;
  export const PiggyBank: ComponentType<IconProps>;
  export const Receipt: ComponentType<IconProps>;
  export const ShoppingCart: ComponentType<IconProps>;
  export const ShoppingBag: ComponentType<IconProps>;
  export const Store: ComponentType<IconProps>;
  export const Storefront: ComponentType<IconProps>;
  export const Building: ComponentType<IconProps>;
  export const Building2: ComponentType<IconProps>;
  export const Factory: ComponentType<IconProps>;
  export const Warehouse: ComponentType<IconProps>;
  export const Office: ComponentType<IconProps>;
  export const School: ComponentType<IconProps>;
  export const University: ComponentType<IconProps>;
  export const Library: ComponentType<IconProps>;
  export const Hospital: ComponentType<IconProps>;
  export const Church: ComponentType<IconProps>;
  export const Mosque: ComponentType<IconProps>;
  export const Synagogue: ComponentType<IconProps>;
  export const Temple: ComponentType<IconProps>;
  export const Castle: ComponentType<IconProps>;
  export const Tower: ComponentType<IconProps>;
  export const Bridge: ComponentType<IconProps>;
  export const Landmark: ComponentType<IconProps>;
  export const Monument: ComponentType<IconProps>;
  export const Statue: ComponentType<IconProps>;
  export const Fountain: ComponentType<IconProps>;
  export const Park: ComponentType<IconProps>;
  export const Garden: ComponentType<IconProps>;
  export const Tree: ComponentType<IconProps>;
  export const TreePine: ComponentType<IconProps>;
  export const TreeDeciduous: ComponentType<IconProps>;
  export const Flower: ComponentType<IconProps>;
  export const Flower2: ComponentType<IconProps>;
  export const Leaf: ComponentType<IconProps>;
  export const Seedling: ComponentType<IconProps>;
  export const Sprout: ComponentType<IconProps>;
  export const Cactus: ComponentType<IconProps>;
  export const Mushroom: ComponentType<IconProps>;
  export const Grass: ComponentType<IconProps>;
  export const Wheat: ComponentType<IconProps>;
  export const Corn: ComponentType<IconProps>;
  export const Carrot: ComponentType<IconProps>;
  export const Apple: ComponentType<IconProps>;
  export const Orange: ComponentType<IconProps>;
  export const Banana: ComponentType<IconProps>;
  export const Grape: ComponentType<IconProps>;
  export const Cherry: ComponentType<IconProps>;
  export const Strawberry: ComponentType<IconProps>;
  export const Lemon: ComponentType<IconProps>;
  export const Lime: ComponentType<IconProps>;
  export const Coconut: ComponentType<IconProps>;
  export const Pineapple: ComponentType<IconProps>;
  export const Watermelon: ComponentType<IconProps>;
  export const Melon: ComponentType<IconProps>;
  export const Peach: ComponentType<IconProps>;
  export const Pear: ComponentType<IconProps>;
  export const Plum: ComponentType<IconProps>;
  export const Kiwi: ComponentType<IconProps>;
  export const Mango: ComponentType<IconProps>;
  export const Avocado: ComponentType<IconProps>;
  export const Tomato: ComponentType<IconProps>;
  export const Potato: ComponentType<IconProps>;
  export const Onion: ComponentType<IconProps>;
  export const Garlic: ComponentType<IconProps>;
  export const Pepper: ComponentType<IconProps>;
  export const Chili: ComponentType<IconProps>;
  export const Cucumber: ComponentType<IconProps>;
  export const Lettuce: ComponentType<IconProps>;
  export const Cabbage: ComponentType<IconProps>;
  export const Broccoli: ComponentType<IconProps>;
  export const Cauliflower: ComponentType<IconProps>;
  export const Spinach: ComponentType<IconProps>;
  export const Celery: ComponentType<IconProps>;
  export const Radish: ComponentType<IconProps>;
  export const Turnip: ComponentType<IconProps>;
  export const Beet: ComponentType<IconProps>;
  export const Eggplant: ComponentType<IconProps>;
  export const Zucchini: ComponentType<IconProps>;
  export const Squash: ComponentType<IconProps>;
  export const Pumpkin: ComponentType<IconProps>;
  export const Gourd: ComponentType<IconProps>;
  export const Bean: ComponentType<IconProps>;
  export const Pea: ComponentType<IconProps>;
  export const Lentil: ComponentType<IconProps>;
  export const Rice: ComponentType<IconProps>;
  export const Bread: ComponentType<IconProps>;
  export const Croissant: ComponentType<IconProps>;
  export const Bagel: ComponentType<IconProps>;
  export const Pretzel: ComponentType<IconProps>;
  export const Donut: ComponentType<IconProps>;
  export const Cookie: ComponentType<IconProps>;
  export const Cake: ComponentType<IconProps>;
  export const Cupcake: ComponentType<IconProps>;
  export const Pie: ComponentType<IconProps>;
  export const IceCream: ComponentType<IconProps>;
  export const Candy: ComponentType<IconProps>;
  export const Chocolate: ComponentType<IconProps>;
  export const Lollipop: ComponentType<IconProps>;
  export const Gum: ComponentType<IconProps>;
  export const Honey: ComponentType<IconProps>;
  export const Jam: ComponentType<IconProps>;
  export const Butter: ComponentType<IconProps>;
  export const Cheese: ComponentType<IconProps>;
  export const Milk: ComponentType<IconProps>;
  export const Yogurt: ComponentType<IconProps>;
  export const Egg: ComponentType<IconProps>;
  export const Meat: ComponentType<IconProps>;
  export const Chicken: ComponentType<IconProps>;
  export const Fish: ComponentType<IconProps>;
  export const Shrimp: ComponentType<IconProps>;
  export const Crab: ComponentType<IconProps>;
  export const Lobster: ComponentType<IconProps>;
  export const Oyster: ComponentType<IconProps>;
  export const Clam: ComponentType<IconProps>;
  export const Mussel: ComponentType<IconProps>;
  export const Scallop: ComponentType<IconProps>;
  export const Squid: ComponentType<IconProps>;
  export const Octopus: ComponentType<IconProps>;
  export const Jellyfish: ComponentType<IconProps>;
  export const Starfish: ComponentType<IconProps>;
  export const Seahorse: ComponentType<IconProps>;
  export const Whale: ComponentType<IconProps>;
  export const Dolphin: ComponentType<IconProps>;
  export const Shark: ComponentType<IconProps>;
  export const Turtle: ComponentType<IconProps>;
  export const Penguin: ComponentType<IconProps>;
  export const Seal: ComponentType<IconProps>;
  export const Walrus: ComponentType<IconProps>;
  export const PolarBear: ComponentType<IconProps>;
  export const Bear: ComponentType<IconProps>;
  export const Wolf: ComponentType<IconProps>;
  export const Fox: ComponentType<IconProps>;
  export const Rabbit: ComponentType<IconProps>;
  export const Squirrel: ComponentType<IconProps>;
  export const Hedgehog: ComponentType<IconProps>;
  export const Raccoon: ComponentType<IconProps>;
  export const Skunk: ComponentType<IconProps>;
  export const Beaver: ComponentType<IconProps>;
  export const Otter: ComponentType<IconProps>;
  export const Deer: ComponentType<IconProps>;
  export const Elk: ComponentType<IconProps>;
  export const Moose: ComponentType<IconProps>;
  export const Bison: ComponentType<IconProps>;
  export const Buffalo: ComponentType<IconProps>;
  export const Cow: ComponentType<IconProps>;
  export const Bull: ComponentType<IconProps>;
  export const Horse: ComponentType<IconProps>;
  export const Pony: ComponentType<IconProps>;
  export const Donkey: ComponentType<IconProps>;
  export const Mule: ComponentType<IconProps>;
  export const Zebra: ComponentType<IconProps>;
  export const Giraffe: ComponentType<IconProps>;
  export const Elephant: ComponentType<IconProps>;
  export const Rhino: ComponentType<IconProps>;
  export const Hippo: ComponentType<IconProps>;
  export const Lion: ComponentType<IconProps>;
  export const Tiger: ComponentType<IconProps>;
  export const Leopard: ComponentType<IconProps>;
  export const Cheetah: ComponentType<IconProps>;
  export const Jaguar: ComponentType<IconProps>;
  export const Panther: ComponentType<IconProps>;
  export const Lynx: ComponentType<IconProps>;
  export const Bobcat: ComponentType<IconProps>;
  export const Cat: ComponentType<IconProps>;
  export const Dog: ComponentType<IconProps>;
  export const Puppy: ComponentType<IconProps>;
  export const Kitten: ComponentType<IconProps>;
  export const Hamster: ComponentType<IconProps>;
  export const GuineaPig: ComponentType<IconProps>;
  export const Ferret: ComponentType<IconProps>;
  export const Rat: ComponentType<IconProps>;
  export const Mouse2: ComponentType<IconProps>;
  export const Bat: ComponentType<IconProps>;
  export const Bird: ComponentType<IconProps>;
  export const Eagle: ComponentType<IconProps>;
  export const Hawk: ComponentType<IconProps>;
  export const Falcon: ComponentType<IconProps>;
  export const Owl: ComponentType<IconProps>;
  export const Parrot: ComponentType<IconProps>;
  export const Toucan: ComponentType<IconProps>;
  export const Flamingo: ComponentType<IconProps>;
  export const Swan: ComponentType<IconProps>;
  export const Duck: ComponentType<IconProps>;
  export const Goose: ComponentType<IconProps>;
  export const Chicken2: ComponentType<IconProps>;
  export const Rooster: ComponentType<IconProps>;
  export const Turkey: ComponentType<IconProps>;
  export const Peacock: ComponentType<IconProps>;
  export const Ostrich: ComponentType<IconProps>;
  export const Emu: ComponentType<IconProps>;
  export const Kiwi2: ComponentType<IconProps>;
  export const Penguin2: ComponentType<IconProps>;
  export const Albatross: ComponentType<IconProps>;
  export const Seagull: ComponentType<IconProps>;
  export const Pelican: ComponentType<IconProps>;
  export const Heron: ComponentType<IconProps>;
  export const Crane: ComponentType<IconProps>;
  export const Stork: ComponentType<IconProps>;
  export const Ibis: ComponentType<IconProps>;
  export const Spoonbill: ComponentType<IconProps>;
  export const Kingfisher: ComponentType<IconProps>;
  export const Woodpecker: ComponentType<IconProps>;
  export const Robin: ComponentType<IconProps>;
  export const Sparrow: ComponentType<IconProps>;
  export const Finch: ComponentType<IconProps>;
  export const Canary: ComponentType<IconProps>;
  export const Cardinal: ComponentType<IconProps>;
  export const BlueJay: ComponentType<IconProps>;
  export const Crow: ComponentType<IconProps>;
  export const Raven: ComponentType<IconProps>;
  export const Magpie: ComponentType<IconProps>;
  export const Pigeon: ComponentType<IconProps>;
  export const Dove: ComponentType<IconProps>;
  export const Hummingbird: ComponentType<IconProps>;
  export const Swallow: ComponentType<IconProps>;
  export const Swift: ComponentType<IconProps>;
  export const Martin: ComponentType<IconProps>;
  export const Wren: ComponentType<IconProps>;
  export const Thrush: ComponentType<IconProps>;
  export const Blackbird: ComponentType<IconProps>;
  export const Nightingale: ComponentType<IconProps>;
  export const Lark: ComponentType<IconProps>;
  export const Warbler: ComponentType<IconProps>;
  export const Flycatcher: ComponentType<IconProps>;
  export const Vireo: ComponentType<IconProps>;
  export const Tanager: ComponentType<IconProps>;
  export const Oriole: ComponentType<IconProps>;
  export const Grosbeak: ComponentType<IconProps>;
  export const Bunting: ComponentType<IconProps>;
  export const Towhee: ComponentType<IconProps>;
  export const Junco: ComponentType<IconProps>;
  export const Chickadee: ComponentType<IconProps>;
  export const Titmouse: ComponentType<IconProps>;
  export const Nuthatch: ComponentType<IconProps>;
  export const Creeper: ComponentType<IconProps>;
  export const Mockingbird: ComponentType<IconProps>;
  export const Catbird: ComponentType<IconProps>;
  export const Thrasher: ComponentType<IconProps>;
  export const Starling: ComponentType<IconProps>;
  export const Waxwing: ComponentType<IconProps>;
  export const Shrike: ComponentType<IconProps>;
  export const Vireo2: ComponentType<IconProps>;
  export const Warbler2: ComponentType<IconProps>;
  export const Redstart: ComponentType<IconProps>;
  export const Ovenbird: ComponentType<IconProps>;
  export const Waterthrush: ComponentType<IconProps>;
  export const Yellowthroat: ComponentType<IconProps>;
  export const Chat: ComponentType<IconProps>;
  export const Tanager2: ComponentType<IconProps>;
  export const Cardinal2: ComponentType<IconProps>;
  export const Grosbeak2: ComponentType<IconProps>;
  export const Bunting2: ComponentType<IconProps>;
  export const Dickcissel: ComponentType<IconProps>;
  export const Bobolink: ComponentType<IconProps>;
  export const Meadowlark: ComponentType<IconProps>;
  export const Blackbird2: ComponentType<IconProps>;
  export const Oriole2: ComponentType<IconProps>;
  export const Grackle: ComponentType<IconProps>;
  export const Cowbird: ComponentType<IconProps>;
  export const Finch2: ComponentType<IconProps>;
  export const Siskin: ComponentType<IconProps>;
  export const Goldfinch: ComponentType<IconProps>;
  export const Canary2: ComponentType<IconProps>;
  export const Redpoll: ComponentType<IconProps>;
  export const Crossbill: ComponentType<IconProps>;
  export const Grosbeak3: ComponentType<IconProps>;
  export const Sparrow2: ComponentType<IconProps>;
  export const Towhee2: ComponentType<IconProps>;
  export const Junco2: ComponentType<IconProps>;
  export const Longspur: ComponentType<IconProps>;
  export const Bunting3: ComponentType<IconProps>;
  export const Lark2: ComponentType<IconProps>;
  export const Pipit: ComponentType<IconProps>;
  export const Wagtail: ComponentType<IconProps>;
  export const Waxwing2: ComponentType<IconProps>;
  export const Shrike2: ComponentType<IconProps>;
  export const Vireo3: ComponentType<IconProps>;
  export const Jay: ComponentType<IconProps>;
  export const Magpie2: ComponentType<IconProps>;
  export const Crow2: ComponentType<IconProps>;
  export const Raven2: ComponentType<IconProps>;
  export const Nutcracker: ComponentType<IconProps>;
  export const Chickadee2: ComponentType<IconProps>;
  export const Titmouse2: ComponentType<IconProps>;
  export const Verdin: ComponentType<IconProps>;
  export const Bushtit: ComponentType<IconProps>;
  export const Nuthatch2: ComponentType<IconProps>;
  export const Creeper2: ComponentType<IconProps>;
  export const Wren2: ComponentType<IconProps>;
  export const Gnatcatcher: ComponentType<IconProps>;
  export const Kinglet: ComponentType<IconProps>;
  export const Thrush2: ComponentType<IconProps>;
  export const Robin2: ComponentType<IconProps>;
  export const Bluebird: ComponentType<IconProps>;
  export const Solitaire: ComponentType<IconProps>;
  export const Veery: ComponentType<IconProps>;
  export const Hermit: ComponentType<IconProps>;
  export const Wood: ComponentType<IconProps>;
  export const Swainson: ComponentType<IconProps>;
  export const Gray: ComponentType<IconProps>;
  export const Bicknell: ComponentType<IconProps>;
  export const Mockingbird2: ComponentType<IconProps>;
  export const Catbird2: ComponentType<IconProps>;
  export const Thrasher2: ComponentType<IconProps>;
  export const Sage: ComponentType<IconProps>;
  export const Bendire: ComponentType<IconProps>;
  export const Curve: ComponentType<IconProps>;
  export const California: ComponentType<IconProps>;
  export const Leconte: ComponentType<IconProps>;
  export const Crissal: ComponentType<IconProps>;
  export const Starling2: ComponentType<IconProps>;
  export const Pipit2: ComponentType<IconProps>;
  export const Wagtail2: ComponentType<IconProps>;
  export const Bohemian: ComponentType<IconProps>;
  export const Cedar: ComponentType<IconProps>;
  export const Phainopepla: ComponentType<IconProps>;
  export const Silky: ComponentType<IconProps>;
  export const Olive: ComponentType<IconProps>;
  export const Red: ComponentType<IconProps>;
  export const White: ComponentType<IconProps>;
  export const Bell: ComponentType<IconProps>;
  export const Gray2: ComponentType<IconProps>;
  export const Plumbeous: ComponentType<IconProps>;
  export const Blue: ComponentType<IconProps>;
  export const Philadelphia: ComponentType<IconProps>;
  export const Warbling: ComponentType<IconProps>;
  export const Tennessee: ComponentType<IconProps>;
  export const Orange: ComponentType<IconProps>;
  export const Nashville: ComponentType<IconProps>;
  export const Virginia: ComponentType<IconProps>;
  export const Colima: ComponentType<IconProps>;
  export const Lucy: ComponentType<IconProps>;
  export const Northern: ComponentType<IconProps>;
  export const Tropical: ComponentType<IconProps>;
  export const Yellow: ComponentType<IconProps>;
  export const Chestnut: ComponentType<IconProps>;
  export const Magnolia: ComponentType<IconProps>;
  export const Cape: ComponentType<IconProps>;
  export const Black: ComponentType<IconProps>;
  export const Cerulean: ComponentType<IconProps>;
  export const Blackburnian: ComponentType<IconProps>;
  export const Pine: ComponentType<IconProps>;
  export const Kirtland: ComponentType<IconProps>;
  export const Prairie: ComponentType<IconProps>;
  export const Palm: ComponentType<IconProps>;
  export const Bay: ComponentType<IconProps>;
  export const Blackpoll: ComponentType<IconProps>;
  export const American: ComponentType<IconProps>;
  export const Prothonotary: ComponentType<IconProps>;
  export const Worm: ComponentType<IconProps>;
  export const Swainson2: ComponentType<IconProps>;
  export const Ovenbird2: ComponentType<IconProps>;
  export const Northern2: ComponentType<IconProps>;
  export const Louisiana: ComponentType<IconProps>;
  export const Kentucky: ComponentType<IconProps>;
  export const Connecticut: ComponentType<IconProps>;
  export const Mourning: ComponentType<IconProps>;
  export const MacGillivray: ComponentType<IconProps>;
  export const Common: ComponentType<IconProps>;
  export const Hooded: ComponentType<IconProps>;
  export const Wilson: ComponentType<IconProps>;
  export const Canada: ComponentType<IconProps>;
  export const Painted: ComponentType<IconProps>;
  export const Yellow2: ComponentType<IconProps>;
  export const Summer: ComponentType<IconProps>;
  export const Scarlet: ComponentType<IconProps>;
  export const Western: ComponentType<IconProps>;
  export const Hepatic: ComponentType<IconProps>;
  export const Northern3: ComponentType<IconProps>;
  export const Rose: ComponentType<IconProps>;
  export const Black2: ComponentType<IconProps>;
  export const Blue2: ComponentType<IconProps>;
  export const Lazuli: ComponentType<IconProps>;
  export const Indigo: ComponentType<IconProps>;
  export const Varied: ComponentType<IconProps>;
  export const Painted2: ComponentType<IconProps>;
  export const Dickcissel2: ComponentType<IconProps>;
  export const Bobolink2: ComponentType<IconProps>;
  export const Red2: ComponentType<IconProps>;
  export const Eastern: ComponentType<IconProps>;
  export const Western2: ComponentType<IconProps>;
  export const Yellow3: ComponentType<IconProps>;
  export const Rusty: ComponentType<IconProps>;
  export const Brewer: ComponentType<IconProps>;
  export const Common2: ComponentType<IconProps>;
  export const Great: ComponentType<IconProps>;
  export const Boat: ComponentType<IconProps>;
  export const Brown: ComponentType<IconProps>;
  export const Bronzed: ComponentType<IconProps>;
  export const Shiny: ComponentType<IconProps>;
  export const Orchard: ComponentType<IconProps>;
  export const Hooded2: ComponentType<IconProps>;
  export const Bullock: ComponentType<IconProps>;
  export const Altamira: ComponentType<IconProps>;
  export const Audubon: ComponentType<IconProps>;
  export const Scott: ComponentType<IconProps>;
  export const Purple: ComponentType<IconProps>;
  export const Cassin: ComponentType<IconProps>;
  export const House: ComponentType<IconProps>;
  export const Red3: ComponentType<IconProps>;
  export const White2: ComponentType<IconProps>;
  export const Pine2: ComponentType<IconProps>;
  export const Lesser: ComponentType<IconProps>;
  export const Lawrence: ComponentType<IconProps>;
  export const American2: ComponentType<IconProps>;
  export const European: ComponentType<IconProps>;
  export const Evening: ComponentType<IconProps>;
  export const Hoary: ComponentType<IconProps>;
  export const Common3: ComponentType<IconProps>;
  export const Red4: ComponentType<IconProps>;
  export const White3: ComponentType<IconProps>;
  export const Two: ComponentType<IconProps>;
  export const Pine3: ComponentType<IconProps>;
  export const Evening2: ComponentType<IconProps>;
  export const House2: ComponentType<IconProps>;
  export const Eurasian: ComponentType<IconProps>;
  export const American3: ComponentType<IconProps>;
  export const Chipping: ComponentType<IconProps>;
  export const Clay: ComponentType<IconProps>;
  export const Brewer2: ComponentType<IconProps>;
  export const Field: ComponentType<IconProps>;
  export const Vesper: ComponentType<IconProps>;
  export const Lark3: ComponentType<IconProps>;
  export const Black3: ComponentType<IconProps>;
  export const Sage2: ComponentType<IconProps>;
  export const Five: ComponentType<IconProps>;
  export const Fox2: ComponentType<IconProps>;
  export const Song: ComponentType<IconProps>;
  export const Lincoln: ComponentType<IconProps>;
  export const Swamp: ComponentType<IconProps>;
  export const White4: ComponentType<IconProps>;
  export const Harris: ComponentType<IconProps>;
  export const Golden: ComponentType<IconProps>;
  export const Dark: ComponentType<IconProps>;
  export const Spotted: ComponentType<IconProps>;
  export const Green: ComponentType<IconProps>;
  export const Abert: ComponentType<IconProps>;
  export const Rufous: ComponentType<IconProps>;
  export const Canyon: ComponentType<IconProps>;
  export const California2: ComponentType<IconProps>;
  export const Rufous2: ComponentType<IconProps>;
  export const Canyon2: ComponentType<IconProps>;
  export const California3: ComponentType<IconProps>;
  export const Rufous3: ComponentType<IconProps>;
  export const Canyon3: ComponentType<IconProps>;
  export const California4: ComponentType<IconProps>;
  export const Rufous4: ComponentType<IconProps>;
  export const Canyon4: ComponentType<IconProps>;
  export const California5: ComponentType<IconProps>;
  export const Rufous5: ComponentType<IconProps>;
  export const Canyon5: ComponentType<IconProps>;
  export const California6: ComponentType<IconProps>;
  export const Rufous6: ComponentType<IconProps>;
  export const Canyon6: ComponentType<IconProps>;
  export const California7: ComponentType<IconProps>;
  export const Rufous7: ComponentType<IconProps>;
  export const Canyon7: ComponentType<IconProps>;
  export const California8: ComponentType<IconProps>;
  export const Rufous8: ComponentType<IconProps>;
  export const Canyon8: ComponentType<IconProps>;
  export const California9: ComponentType<IconProps>;
  export const Rufous9: ComponentType<IconProps>;
  export const Canyon9: ComponentType<IconProps>;
  export const California10: ComponentType<IconProps>;
  export const Rufous10: ComponentType<IconProps>;
  export const Canyon10: ComponentType<IconProps>;
  export const California11: ComponentType<IconProps>;
  export const Rufous11: ComponentType<IconProps>;
  export const Canyon11: ComponentType<IconProps>;
  export const California12: ComponentType<IconProps>;
  export const Rufous12: ComponentType<IconProps>;
  export const Canyon12: ComponentType<IconProps>;
  export const California13: ComponentType<IconProps>;
  export const Rufous13: ComponentType<IconProps>;
  export const Canyon13: ComponentType<IconProps>;
  export const California14: ComponentType<IconProps>;
  export const Rufous14: ComponentType<IconProps>;
  export const Canyon14: ComponentType<IconProps>;
  export const California15: ComponentType<IconProps>;
  export const Rufous15: ComponentType<IconProps>;
  export const Canyon15: ComponentType<IconProps>;
  export const California16: ComponentType<IconProps>;
  export const Rufous16: ComponentType<IconProps>;
  export const Canyon16: ComponentType<IconProps>;
  export const California17: ComponentType<IconProps>;
  export const Rufous17: ComponentType<IconProps>;
  export const Canyon17: ComponentType<IconProps>;
  export const California18: ComponentType<IconProps>;
  export const Rufous18: ComponentType<IconProps>;
  export const Canyon18: ComponentType<IconProps>;
  export const California19: ComponentType<IconProps>;
  export const Rufous19: ComponentType<IconProps>;
  export const Canyon19: ComponentType<IconProps>;
  export const California20: ComponentType<IconProps>;
  export const Rufous20: ComponentType<IconProps>;
  export const Canyon20: ComponentType<IconProps>;
  export const California21: ComponentType<IconProps>;
  export const Rufous21: ComponentType<IconProps>;
  export const Canyon21: ComponentType<IconProps>;
  export const California22: ComponentType<IconProps>;
  export const Rufous22: ComponentType<IconProps>;
  export const Canyon22: ComponentType<IconProps>;
  export const California23: ComponentType<IconProps>;
  export const Rufous23: ComponentType<IconProps>;
  export const Canyon23: ComponentType<IconProps>;
  export const California24: ComponentType<IconProps>;
  export const Rufous24: ComponentType<IconProps>;
  export const Canyon24: ComponentType<IconProps>;
  export const California25: ComponentType<IconProps>;
  export const Rufous25: ComponentType<IconProps>;
  export const Canyon25: ComponentType<IconProps>;
  export const California26: ComponentType<IconProps>;
  export const Rufous26: ComponentType<IconProps>;
  export const Canyon26: ComponentType<IconProps>;
  export const California27: ComponentType<IconProps>;
  export const Rufous27: ComponentType<IconProps>;
  export const Canyon27: ComponentType<IconProps>;
  export const California28: ComponentType<IconProps>;
  export const Rufous28: ComponentType<IconProps>;
  export const Canyon28: ComponentType<IconProps>;
  export const California29: ComponentType<IconProps>;
  export const Rufous29: ComponentType<IconProps>;
  export const Canyon29: ComponentType<IconProps>;
  export const California30: ComponentType<IconProps>;
  export const Rufous30: ComponentType<IconProps>;
  export const Canyon30: ComponentType<IconProps>;
  export const California31: ComponentType<IconProps>;
  export const Rufous31: ComponentType<IconProps>;
  export const Canyon31: ComponentType<IconProps>;
  export const California32: ComponentType<IconProps>;
  export const Rufous32: ComponentType<IconProps>;
  export const Canyon32: ComponentType<IconProps>;
  export const California33: ComponentType<IconProps>;
  export const Rufous33: ComponentType<IconProps>;
  export const Canyon33: ComponentType<IconProps>;
  export const California34: ComponentType<IconProps>;
  export const Rufous34: ComponentType<IconProps>;
  export const Canyon34: ComponentType<IconProps>;
  export const California35: ComponentType<IconProps>;
  export const Rufous35: ComponentType<IconProps>;
  export const Canyon35: ComponentType<IconProps>;
  export const California36: ComponentType<IconProps>;
  export const Rufous36: ComponentType<IconProps>;
  export const Canyon36: ComponentType<IconProps>;
  export const California37: ComponentType<IconProps>;
  export const Rufous37: ComponentType<IconProps>;
  export const Canyon37: ComponentType<IconProps>;
  export const California38: ComponentType<IconProps>;
  export const Rufous38: ComponentType<IconProps>;
  export const Canyon38: ComponentType<IconProps>;
  export const California39: ComponentType<IconProps>;
  export const Rufous39: ComponentType<IconProps>;
  export const Canyon39: ComponentType<IconProps>;
  export const California40: ComponentType<IconProps>;
  export const Rufous40: ComponentType<IconProps>;
  export const Canyon40: ComponentType<IconProps>;
  export const California41: ComponentType<IconProps>;
  export const Rufous41: ComponentType<IconProps>;
  export const Canyon41: ComponentType<IconProps>;
  export const California42: ComponentType<IconProps>;
  export const Rufous42: ComponentType<IconProps>;
  export const Canyon42: ComponentType<IconProps>;
  export const California43: ComponentType<IconProps>;
  export const Rufous43: ComponentType<IconProps>;
  export const Canyon43: ComponentType<IconProps>;
  export const California44: ComponentType<IconProps>;
  export const Rufous44: ComponentType<IconProps>;
  export const Canyon44: ComponentType<IconProps>;
  export const California45: ComponentType<IconProps>;
  export const Rufous45: ComponentType<IconProps>;
  export const Canyon45: ComponentType<IconProps>;
  export const California46: ComponentType<IconProps>;
  export const Rufous46: ComponentType<IconProps>;
  export const Canyon46: ComponentType<IconProps>;
  export const California47: ComponentType<IconProps>;
  export const Rufous47: ComponentType<IconProps>;
  export const Canyon47: ComponentType<IconProps>;
  export const California48: ComponentType<IconProps>;
  export const Rufous48: ComponentType<IconProps>;
  export const Canyon48: ComponentType<IconProps>;
  export const California49: ComponentType<IconProps>;
  export const Rufous49: ComponentType<IconProps>;
  export const Canyon49: ComponentType<IconProps>;
  export const California50: ComponentType<IconProps>;
  export const Rufous50: ComponentType<IconProps>;
  export const Canyon50: ComponentType<IconProps>;
  export const California51: ComponentType<IconProps>;
  export const Rufous51: ComponentType<IconProps>;
  export const Canyon51: ComponentType<IconProps>;
  export const California52: ComponentType<IconProps>;
  export const Rufous52: ComponentType<IconProps>;
  export const Canyon52: ComponentType<IconProps>;
  export const California53: ComponentType<IconProps>;
  export const Rufous53: ComponentType<IconProps>;
  export const Canyon53: ComponentType<IconProps>;
  export const California54: ComponentType<IconProps>;
  export const Rufous54: ComponentType<IconProps>;
  export const Canyon54: ComponentType<IconProps>;
  export const California55: ComponentType<IconProps>;
  export const Rufous55: ComponentType<IconProps>;
  export const Canyon55: ComponentType<IconProps>;
  export const California56: ComponentType<IconProps>;
  export const Rufous56: ComponentType<IconProps>;
  export const Canyon56: ComponentType<IconProps>;
  export const California57: ComponentType<IconProps>;
  export const Rufous57: ComponentType<IconProps>;
  export const Canyon57: ComponentType<IconProps>;
  export const California58: ComponentType<IconProps>;
  export const Rufous58: ComponentType<IconProps>;
  export const Canyon58: ComponentType<IconProps>;
  export const California59: ComponentType<IconProps>;
  export const Rufous59: ComponentType<IconProps>;
  export const Canyon59: ComponentType<IconProps>;
  export const California60: ComponentType<IconProps>;
  export const Rufous60: ComponentType<IconProps>;
  export const Canyon60: ComponentType<IconProps>;
  export const California61: ComponentType<IconProps>;
  export const Rufous61: ComponentType<IconProps>;
  export const Canyon61: ComponentType<IconProps>;
  export const California62: ComponentType<IconProps>;
  export const Rufous62: ComponentType<IconProps>;
  export const Canyon62: ComponentType<IconProps>;
  export const California63: ComponentType<IconProps>;
  export const Rufous63: ComponentType<IconProps>;
  export const Canyon63: ComponentType<IconProps>;
  export const California64: ComponentType<IconProps>;
  export const Rufous64: ComponentType<IconProps>;
  export const Canyon64: ComponentType<IconProps>;
  export const California65: ComponentType<IconProps>;
  export const Rufous65: ComponentType<IconProps>;
  export const Canyon65: ComponentType<IconProps>;
  export const California66: ComponentType<IconProps>;
  export const Rufous66: ComponentType<IconProps>;
  export const Canyon66: ComponentType<IconProps>;
  export const California67: ComponentType<IconProps>;
  export const Rufous67: ComponentType<IconProps>;
  export const Canyon67: ComponentType<IconProps>;
  export const California68: ComponentType<IconProps>;
  export const Rufous68: ComponentType<IconProps>;
  export const Canyon68: ComponentType<IconProps>;
  export const California69: ComponentType<IconProps>;
  export const Rufous69: ComponentType<IconProps>;
  export const Canyon69: ComponentType<IconProps>;
  export const California70: ComponentType<IconProps>;
  export const Rufous70: ComponentType<IconProps>;
  export const Canyon70: ComponentType<IconProps>;
  export const California71: ComponentType<IconProps>;
  export const Rufous71: ComponentType<IconProps>;
  export const Canyon71: ComponentType<IconProps>;
  export const California72: ComponentType<IconProps>;
  export const Rufous72: ComponentType<IconProps>;
  export const Canyon72: ComponentType<IconProps>;
  export const California73: ComponentType<IconProps>;
  export const Rufous73: ComponentType<IconProps>;
  export const Canyon73: ComponentType<IconProps>;
  export const California74: ComponentType<IconProps>;
  export const Rufous74: ComponentType<IconProps>;
  export const Canyon74: ComponentType<IconProps>;
  export const California75: ComponentType<IconProps>;
  export const Rufous75: ComponentType<IconProps>;
  export const Canyon75: ComponentType<IconProps>;
  export const California76: ComponentType<IconProps>;
  export const Rufous76: ComponentType<IconProps>;
  export const Canyon76: ComponentType<IconProps>;
  export const California77: ComponentType<IconProps>;
  export const Rufous77: ComponentType<IconProps>;
  export const Canyon77: ComponentType<IconProps>;
  export const California78: ComponentType<IconProps>;
  export const Rufous78: ComponentType<IconProps>;
  export const Canyon78: ComponentType<IconProps>;
  export const California79: ComponentType<IconProps>;
  export const Rufous79: ComponentType<IconProps>;
  export const Canyon79: ComponentType<IconProps>;
  export const California80: ComponentType<IconProps>;
  export const Rufous80: ComponentType<IconProps>;
  export const Canyon80: ComponentType<IconProps>;
  export const California81: ComponentType<IconProps>;
  export const Rufous81: ComponentType<IconProps>;
  export const Canyon81: ComponentType<IconProps>;
  export const California82: ComponentType<IconProps>;
  export const Rufous82: ComponentType<IconProps>;
  export const Canyon82: ComponentType<IconProps>;
  export const California83: ComponentType<IconProps>;
  export const Rufous83: ComponentType<IconProps>;
  export const Canyon83: ComponentType<IconProps>;
  export const California84: ComponentType<IconProps>;
  export const Rufous84: ComponentType<IconProps>;
  export const Canyon84: ComponentType<IconProps>;
  export const California85: ComponentType<IconProps>;
  export const Rufous85: ComponentType<IconProps>;
  export const Canyon85: ComponentType<IconProps>;
  export const California86: ComponentType<IconProps>;
  export const Rufous86: ComponentType<IconProps>;
  export const Canyon86: ComponentType<IconProps>;
  export const California87: ComponentType<IconProps>;
  export const Rufous87: ComponentType<IconProps>;
  export const Canyon87: ComponentType<IconProps>;
  export const California88: ComponentType<IconProps>;
  export const Rufous88: ComponentType<IconProps>;
  export const Canyon88: ComponentType<IconProps>;
  export const California89: ComponentType<IconProps>;
  export const Rufous89: ComponentType<IconProps>;
  export const Canyon89: ComponentType<IconProps>;
  export const California90: ComponentType<IconProps>;
  export const Rufous90: ComponentType<IconProps>;
  export const Canyon90: ComponentType<IconProps>;
  export const California91: ComponentType<IconProps>;
  export const Rufous91: ComponentType<IconProps>;
  export const Canyon91: ComponentType<IconProps>;
  export const California92: ComponentType<IconProps>;
  export const Rufous92: ComponentType<IconProps>;
  export const Canyon92: ComponentType<IconProps>;
  export const California93: ComponentType<IconProps>;
  export const Rufous93: ComponentType<IconProps>;
  export const Canyon93: ComponentType<IconProps>;
  export const California94: ComponentType<IconProps>;
  export const Rufous94: ComponentType<IconProps>;
  export const Canyon94: ComponentType<IconProps>;
  export const California95: ComponentType<IconProps>;
  export const Rufous95: ComponentType<IconProps>;
  export const Canyon95: ComponentType<IconProps>;
  export const California96: ComponentType<IconProps>;
  export const Rufous96: ComponentType<IconProps>;
  export const Canyon96: ComponentType<IconProps>;
  export const California97: ComponentType<IconProps>;
  export const Rufous97: ComponentType<IconProps>;
  export const Canyon97: ComponentType<IconProps>;
  export const California98: ComponentType<IconProps>;
  export const Rufous98: ComponentType<IconProps>;
  export const Canyon98: ComponentType<IconProps>;
  export const California99: ComponentType<IconProps>;
  export const Rufous99: ComponentType<IconProps>;
  export const Canyon99: ComponentType<IconProps>;
  export const California100: ComponentType<IconProps>;
  export const Rufous100: ComponentType<IconProps>;
  export const Canyon100: ComponentType<IconProps>;

  // Additional missing icons
  export const MessageSquare: ComponentType<IconProps>;
  export const Calculator: ComponentType<IconProps>;
  export const CheckSquare: ComponentType<IconProps>;
  export const Trophy: ComponentType<IconProps>;
  export const Shield: ComponentType<IconProps>;
  export const Flame: ComponentType<IconProps>;
  export const PlayCircle: ComponentType<IconProps>;
  export const PauseCircle: ComponentType<IconProps>;
  export const Coffee: ComponentType<IconProps>;
  export const Square: ComponentType<IconProps>;
  export const Share: ComponentType<IconProps>;
  export const Palette: ComponentType<IconProps>;
  export const Edit: ComponentType<IconProps>;
  export const ThumbsUp: ComponentType<IconProps>;
  export const Type: ComponentType<IconProps>;
  export const Heading1: ComponentType<IconProps>;
  export const Heading2: ComponentType<IconProps>;
  export const Heading3: ComponentType<IconProps>;
  export const ListOrdered: ComponentType<IconProps>;
  export const Quote: ComponentType<IconProps>;
  export const Table: ComponentType<IconProps>;
  export const MoreHorizontal: ComponentType<IconProps>;
  export const Phone: ComponentType<IconProps>;
  export const MapPin: ComponentType<IconProps>;
  export const Github: ComponentType<IconProps>;
  export const Twitter: ComponentType<IconProps>;
  export const Linkedin: ComponentType<IconProps>;
  export const UserCheck: ComponentType<IconProps>;
}

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: string;
      image?: string;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: string;
    image?: string;
  }

  interface NextAuthConfig {
    providers: any[];
    adapter?: any;
    session?: any;
    callbacks?: any;
    pages?: any;
    events?: any;
    logger?: any;
    debug?: boolean;
  }

  export default function NextAuth(config: NextAuthConfig): {
    handlers: any;
    auth: any;
    signIn: any;
    signOut: any;
  };
}

declare module 'next-auth/react' {
  export interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: string;
      image?: string;
    };
  }

  export function useSession(): {
    data: Session | null;
    status: 'loading' | 'authenticated' | 'unauthenticated';
  };

  export function signIn(provider?: string, options?: any): Promise<any>;
  export function signOut(options?: any): Promise<any>;
  export function getSession(): Promise<Session | null>;

  export function SessionProvider(props: {
    children: React.ReactNode;
    session?: Session | null;
  }): JSX.Element;
}

declare module 'next-auth/providers/google' {
  export default function Google(config: any): any;
}

declare module 'next-auth/providers/credentials' {
  export default function Credentials(config: any): any;
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: string;
  }

  export function getToken(req: any): Promise<JWT | null>;
}

declare module '@prisma/client' {
  export interface User {
    id: string;
    email: string;
    name: string;
    password?: string;
    role: Role;
    image?: string;
    emailVerified?: Date;
    createdAt: Date;
    updatedAt: Date;
    accounts: Account[];
    sessions: Session[];
    todos: Todo[];
    classes: Class[];
    assignments: Assignment[];
    notifications: Notification[];
  }

  export interface Account {
    id: string;
    userId: string;
    type: string;
    provider: string;
    providerAccountId: string;
    refresh_token?: string;
    access_token?: string;
    expires_at?: number;
    token_type?: string;
    scope?: string;
    id_token?: string;
    session_state?: string;
    user: User;
  }

  export interface Session {
    id: string;
    sessionToken: string;
    userId: string;
    expires: Date;
    user: User;
  }

  export interface VerificationToken {
    identifier: string;
    token: string;
    expires: Date;
  }

  export interface Todo {
    id: string;
    title: string;
    description?: string;
    completed: boolean;
    priority: Priority;
    dueDate?: Date;
    source: TodoSource;
    sourceId?: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
    user: User;
  }

  export interface Class {
    id: string;
    name: string;
    description?: string;
    code: string;
    semester: string;
    year: number;
    credits: number;
    instructor: string;
    schedule?: string;
    location?: string;
    color: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
    user: User;
    assignments: Assignment[];
  }

  export interface Assignment {
    id: string;
    title: string;
    description?: string;
    dueDate: Date;
    priority: Priority;
    completed: boolean;
    grade?: number;
    maxGrade?: number;
    weight?: number;
    classId: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
    class: Class;
    user: User;
  }

  export interface Notification {
    id: string;
    title: string;
    message: string;
    type: NotificationType;
    isRead: boolean;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
    user: User;
  }

  export enum Role {
    STUDENT = 'STUDENT',
    TEACHER = 'TEACHER',
    ADMIN = 'ADMIN'
  }

  export enum Priority {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    URGENT = 'URGENT'
  }

  export enum TodoSource {
    MANUAL = 'MANUAL',
    ASSIGNMENT = 'ASSIGNMENT',
    AI_GENERATED = 'AI_GENERATED',
    TEACHER_ASSIGNMENT = 'TEACHER_ASSIGNMENT',
    AI_LEARNING_PATH = 'AI_LEARNING_PATH',
    PERSONAL = 'PERSONAL'
  }

  export enum TodoStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
    OVERDUE = 'OVERDUE'
  }

  export enum AssignmentStatus {
    DRAFT = 'DRAFT',
    PUBLISHED = 'PUBLISHED',
    SUBMITTED = 'SUBMITTED',
    GRADED = 'GRADED'
  }

  export enum GroupRole {
    MEMBER = 'MEMBER',
    ADMIN = 'ADMIN',
    MODERATOR = 'MODERATOR'
  }

  export enum NotificationType {
    INFO = 'INFO',
    SUCCESS = 'SUCCESS',
    WARNING = 'WARNING',
    ERROR = 'ERROR'
  }

  export interface AiLearningPath {
    id: string;
    title: string;
    description?: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedHours: number;
    subject: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
    user: User;
    steps: AiLearningStep[];
  }

  export interface AiLearningStep {
    id: string;
    title: string;
    description?: string;
    content: string;
    order: number;
    completed: boolean;
    pathId: string;
    createdAt: Date;
    updatedAt: Date;
    path: AiLearningPath;
  }

  export interface Group {
    id: string;
    name: string;
    description?: string;
    isPrivate: boolean;
    createdAt: Date;
    updatedAt: Date;
    members: GroupMember[];
  }

  export interface GroupMember {
    id: string;
    userId: string;
    groupId: string;
    role: GroupRole;
    joinedAt: Date;
    user: User;
    group: Group;
  }

  export interface ClassMember {
    id: string;
    userId: string;
    classId: string;
    role: 'STUDENT' | 'TEACHER' | 'ASSISTANT';
    joinedAt: Date;
    user: User;
    class: Class;
  }

  export class PrismaClient {
    user: any;
    account: any;
    session: any;
    verificationToken: any;
    todo: any;
    class: any;
    assignment: any;
    notification: any;
    aiLearningPath: any;
    aiLearningStep: any;
    group: any;
    groupMember: any;
    classMember: any;

    constructor(options?: any);
    $connect(): Promise<void>;
    $disconnect(): Promise<void>;
    $transaction<T>(fn: (prisma: PrismaClient) => Promise<T>): Promise<T>;
  }
}

declare module 'next-themes/dist/types' {
  export interface ThemeProviderProps {
    children: React.ReactNode;
    attribute?: string;
    defaultTheme?: string;
    enableSystem?: boolean;
    disableTransitionOnChange?: boolean;
  }
}

declare module '@hookform/resolvers/zod' {
  import { Resolver } from 'react-hook-form';
  import { ZodSchema } from 'zod';
  
  export function zodResolver<T>(schema: ZodSchema<T>): Resolver<T>;
}

declare module 'react-hook-form' {
  export interface FieldValues {
    [key: string]: any;
  }

  export type FieldPath<T extends FieldValues> = keyof T;

  export interface UseFormReturn<T extends FieldValues = FieldValues> {
    register: any;
    handleSubmit: any;
    formState: any;
    control: any;
    setValue: any;
    getValues: any;
    watch: any;
    reset: any;
    trigger: any;
    clearErrors: any;
    setError: any;
  }

  export function useForm<T extends FieldValues = FieldValues>(options?: any): UseFormReturn<T>;
  export function useFormContext<T extends FieldValues = FieldValues>(): UseFormReturn<T>;
  
  export interface FormProviderProps<T extends FieldValues = FieldValues> {
    children: React.ReactNode;
  }
  
  export function FormProvider<T extends FieldValues = FieldValues>(props: FormProviderProps<T> & UseFormReturn<T>): JSX.Element;
}
