'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  Clock, 
  Coffee,
  Target,
  TrendingUp,
  Calendar
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { cn } from '@/lib/utils';

interface StudySession {
  id: string;
  subject: string;
  duration: number;
  completedAt: Date;
  type: 'focus' | 'break';
}

export default function StudyTimerPage() {
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes in seconds
  const [isRunning, setIsRunning] = useState(false);
  const [mode, setMode] = useState<'focus' | 'shortBreak' | 'longBreak'>('focus');
  const [sessions, setSessions] = useState<StudySession[]>([]);
  const [currentSubject, setCurrentSubject] = useState('');
  const [completedPomodoros, setCompletedPomodoros] = useState(0);
  const [todayStats, setTodayStats] = useState({
    totalMinutes: 0,
    sessions: 0,
    subjects: new Set<string>(),
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Use the imported components
  const playNotificationSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(console.error);
    }
  };

  const modes = {
    focus: { duration: 25 * 60, label: 'Focus Time', color: 'bg-red-500' },
    shortBreak: { duration: 5 * 60, label: 'Short Break', color: 'bg-green-500' },
    longBreak: { duration: 15 * 60, label: 'Long Break', color: 'bg-blue-500' },
  };

  useEffect(() => {
    // Load saved data from localStorage
    const savedSessions = localStorage.getItem('studySessions');
    if (savedSessions) {
      const parsed = JSON.parse(savedSessions);
      setSessions(parsed.map((s: { completedAt: string; [key: string]: unknown }) => ({ ...s, completedAt: new Date(s.completedAt) })));
    }

    const savedPomodoros = localStorage.getItem('completedPomodoros');
    if (savedPomodoros) {
      setCompletedPomodoros(parseInt(savedPomodoros));
    }

    // Calculate today's stats
    calculateTodayStats();
  }, []);

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, timeLeft]);

  useEffect(() => {
    if (timeLeft === 0 && isRunning) {
      handleTimerComplete();
    }
  }, [timeLeft, isRunning]);

  const calculateTodayStats = () => {
    const today = new Date().toDateString();
    const todaySessions = sessions.filter(s => s.completedAt.toDateString() === today);
    
    const totalMinutes = todaySessions.reduce((sum, s) => sum + Math.floor(s.duration / 60), 0);
    const subjects = new Set(todaySessions.map(s => s.subject).filter(Boolean));
    
    setTodayStats({
      totalMinutes,
      sessions: todaySessions.length,
      subjects,
    });
  };

  const handleTimerComplete = () => {
    setIsRunning(false);
    playNotificationSound();
    
    if (mode === 'focus') {
      const newSession: StudySession = {
        id: Date.now().toString(),
        subject: currentSubject || 'General Study',
        duration: modes[mode].duration,
        completedAt: new Date(),
        type: 'focus',
      };
      
      const updatedSessions = [...sessions, newSession];
      setSessions(updatedSessions);
      localStorage.setItem('studySessions', JSON.stringify(updatedSessions));
      
      const newPomodoros = completedPomodoros + 1;
      setCompletedPomodoros(newPomodoros);
      localStorage.setItem('completedPomodoros', newPomodoros.toString());
      
      toast.success(
        <div className="flex items-center gap-2">
          <Coffee className="h-4 w-4" />
          🍅 Pomodoro completed! Time for a break.
        </div>
      );
      
      // Auto-switch to break
      if (newPomodoros % 4 === 0) {
        setMode('longBreak');
        setTimeLeft(modes.longBreak.duration);
      } else {
        setMode('shortBreak');
        setTimeLeft(modes.shortBreak.duration);
      }
    } else {
      toast.success('Break time over! Ready to focus?');
      setMode('focus');
      setTimeLeft(modes.focus.duration);
    }
    
    calculateTodayStats();
  };



  const startTimer = () => {
    if (!currentSubject.trim() && mode === 'focus') {
      toast.error('Please enter what you\'re studying');
      return;
    }
    setIsRunning(true);
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTimeLeft(modes[mode].duration);
  };

  const switchMode = (newMode: 'focus' | 'shortBreak' | 'longBreak') => {
    setMode(newMode);
    setTimeLeft(modes[newMode].duration);
    setIsRunning(false);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const progress = ((modes[mode].duration - timeLeft) / modes[mode].duration) * 100;

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Study Timer</h1>
        <p className="text-gray-600">
          Use the Pomodoro Technique to boost your productivity
        </p>
      </div>

      {/* Timer Card */}
      <Card className="text-center">
        <CardHeader>
          <div className="flex justify-center gap-2 mb-4">
            {Object.entries(modes).map(([key, config]) => (
              <Button
                key={key}
                variant={mode === key ? 'default' : 'outline'}
                size="sm"
                onClick={() => switchMode(key === 'pomodoro' ? 'focus' : key as 'shortBreak' | 'longBreak')}
                disabled={isRunning}
                className={cn(
                  mode === key && config.color
                )}
              >
                {config.label}
              </Button>
            ))}
          </div>
          
          <CardTitle className="text-6xl font-mono mb-4">
            {formatTime(timeLeft)}
          </CardTitle>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div 
              className={cn(
                "h-2 rounded-full transition-all duration-1000",
                modes[mode].color
              )}
              style={{ width: `${progress}%` }}
            />
          </div>
          
          {mode === 'focus' && (
            <Input
              placeholder="What are you studying?"
              value={currentSubject}
              onChange={(e) => setCurrentSubject(e.target.value)}
              className="max-w-md mx-auto mb-4"
              disabled={isRunning}
            />
          )}
        </CardHeader>
        
        <CardContent>
          <div className="flex justify-center gap-4">
            {!isRunning ? (
              <Button onClick={startTimer} size="lg" className="px-8">
                <Play className="h-5 w-5 mr-2" />
                Start
              </Button>
            ) : (
              <Button onClick={pauseTimer} size="lg" variant="outline" className="px-8">
                <Pause className="h-5 w-5 mr-2" />
                Pause
              </Button>
            )}
            
            <Button onClick={resetTimer} size="lg" variant="outline">
              <Square className="h-5 w-5 mr-2" />
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{completedPomodoros}</div>
            <div className="text-sm text-gray-600">Total Pomodoros</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{todayStats.totalMinutes}</div>
            <div className="text-sm text-gray-600">Minutes Today</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{todayStats.sessions}</div>
            <div className="text-sm text-gray-600">Sessions Today</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Calendar className="h-8 w-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{todayStats.subjects.size}</div>
            <div className="text-sm text-gray-600">Subjects Today</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Sessions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Sessions</CardTitle>
          <CardDescription>Your latest study sessions</CardDescription>
        </CardHeader>
        <CardContent>
          {sessions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No study sessions yet. Start your first Pomodoro!</p>
            </div>
          ) : (
            <div className="space-y-3">
              {sessions.slice(-10).reverse().map((session) => (
                <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div>
                      <div className="font-medium">{session.subject}</div>
                      <div className="text-sm text-gray-500">
                        {Math.floor(session.duration / 60)} minutes
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {session.completedAt.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
